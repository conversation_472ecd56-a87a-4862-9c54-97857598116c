<template>
  <div style="display: flex; flex-direction: column; background: #ffffff; margin-top: 2px;width: 100%;">
    <a-spin :spinning="spinning" tip="加载中......">
      <!-- 内容 -->
      <div style="position: relative; display: flex; flex-direction: row; height: 7.71rem; ">
        <!-- 地图 -->
        <div style="flex: 6; height: 100%;width: 100%;  position: relative">
          <MapBox @onMapMounted="onMapMounted" />
          <!-- 水闸过程曲线 -->
          <div class="curve-panel" v-if="!!activeProcess">
            <div class="left">
              <div class="header">
                <div class="name">{{ activeProcess.projectName }}</div>
              </div>

              <div>
                <div class="indicator">
                  <div class="label">上游水位:</div>
                  <div class="value">{{ activeProcess.upWlv }}m</div>
                </div>
                <div class="indicator">
                  <div class="label">下游水位:</div>
                  <div class="value">{{ activeProcess.downWlv }}m</div>
                </div>
                <div class="indicator">
                  <div class="label">过闸流量:</div>
                  <div class="value">{{ activeProcess.q }}m³/s</div>
                </div>
              </div>

              <div style="text-align: center; margin-bottom: 10px">
                <a-button type="primary" size="small" @click.stop="activeProcess = null">收起曲线</a-button>
              </div>
            </div>
            <div class="right">
              <LineEchart :height="'210px'" :width="'100%'" :dataSource="lineChartDataShuiZha"
                :custom="lineChartCustomShuiZha">
              </LineEchart>
            </div>
          </div>

        </div>
        <div style="flex: 5; height: 100%; width: 100%; padding-left: 20px; display: flex; flex-direction: column;">
          <!-- 表数据 -->
          <div style="flex: 39; width: 100%; height: 100%;">
            <div style="height: 100%; display: flex; flex-direction: column">
              <a-tabs v-model="hedaoName" size="small" :animated="false">
                <a-tab-pane v-for="(el, idx) in allData" :key="el.projectCode" :tab="el.projectName"></a-tab-pane>
              </a-tabs>

              <template v-for="(el, idx) in allData">
                <div style="flex: 1" v-if="hedaoName == el.projectCode">
                  <VxeTable :key="el.projectCode" ref="vxeTableRef" size="small" :isShowTableHeader="false"
                    :isDrop="false" :columns="el.columns" :tableData="el.resVOS" :tablePage="false"
                    :scrollY="{ enabled: true, gt: 0 }" :scrollX="{ enabled: true, gt: 0 }"></VxeTable>
                </div>
              </template>
            </div>
          </div>
          <!-- 折线图 -->
          <div style="flex: 22; position: relative; height: 100%; width: 100%;">
            <LineEchart :height="'200px'" :width="'800px'" style="margin-top: 20px;" :dataSource="lineChartData1"
              ref="modelLineChartRef" :custom="lineChartCustom1"></LineEchart>
          </div>
          <div style="flex: 1;width: 100%; height: 44px">
            <TimePlaySlider v-if="times.length && !!mapIns" :times="times" @onTimeChange="onTimeChange" />
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import ResultTable from './component/ResultTable.vue'
import ProcessChart from './component/ProcessChart.vue'
import { getInferRes, getChSimResList } from './services.js'
import MapBox from './component/MapBox/index.vue'
import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
import VxeTable from '@/components/VxeTable/index.vue'
import { mapBoundGeo } from '@/utils/mapBounds.js'
import axios from 'axios'
import { LineEchart } from '@/components/Echarts'
import MultiPolygon from '@/views/schedule/simulation-model/component/MapBox/MultiPolygon.vue'
import 'mapbox-gl/dist/mapbox-gl.css'
import { extractData, getChartsData } from './Utils.js'

export default {
  name: 'ManualForecast',
  components: { MapBox, MultiPolygon, LineEchart, TimePlaySlider, ResultTable, ProcessChart, VxeTable },
  props: ['chSimId', 'modelId'],
  mapIns: null,
  data() {
    return {
      spinning: false,
      activeProcess: null,
      shuizhaGeojsonData: {},
      dataList: [],
      mapOverlayIns: null,
      times: [],
      geojson: null,
      showPopupItem: [],
      forecastOptions: [],
      dataSource: null,
      isTableExpanded: true,
      chartKey: 0,
      currentTime: null,
      lineChartData1: [],
      lineChartCustom1: {
        shortValue: true, // 缩写坐标值
        xLabel: '', // x轴名称
        yLabel: '水位/渠底高程(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        rYUnit: '', // 右侧y轴单位
        rYLabel: '流量(m³/s)', // 右侧y轴名称
        dataZoom: false,
        color: null,
        grid: {
          left: '1%',
          right: '0%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '65%',
        xAxisData: [],
      },
      columnsDrainage1: [
        { type: 'seq', title: '序号', width: 50 },
        {
          title: '时间',
          field: 'time',
          minWidth: 180,
          showOverflow: 'tooltip',
        },
        {
          title: '水位(m)',
          field: 'wlevel',
          minWidth: 180,
          showOverflow: 'tooltip',
        },
        {
          title: '流量(m³/s)',
          field: 'q',
          minWidth: 180,
          showOverflow: 'tooltip',
        },
      ],
      listDrainage1: [],
      hedaoName: '',
      lineOptions: [],
      lineChartCustomShuiZha: {
        shortValue: true, // 缩写坐标值
        xLabel: '', // x轴名称
        yLabel: '水位(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        rYUnit: '', // 右侧y轴单位
        rYLabel: '流量(m³/s)', // 右侧y轴名称
        rYInverse: false, // 右侧y轴是否反向
        yNameLocation: "end",
        dataZoom: false,
        color: null,
        grid: {
          left: '1%',
          right: '1%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '20%',
        xAxisData: []
      },
      lineChartDataShuiZha: [],
      allData: [],
      active: undefined,
    }
  },
  watch: {
    hedaoName(newVal, oldVal) {
      if (newVal != oldVal) {
        let { wlevel, q, stakes, maxWlevel, minWlevel, maxQ, minQ, bottom } = getChartsData(newVal, this.currentTime, this.modelData)
        let res = [
          {
            name: '水位',
            color: '#507EF7',
            yAxisIndex: 0,
            data: wlevel,
          },
          {
            name: '流量',
            color: '#B5E241',
            yAxisIndex: 1,
            data: q,
          },
          {
            name: '渠底高程',
            color: 'gray',
            yAxisIndex: 0,
            data: bottom,
          },
        ]
        this.lineChartCustom1.xAxisData = stakes
        this.lineChartCustom1.yMax0 = maxWlevel
        this.lineChartCustom1.yMin0 = minWlevel
        this.lineChartCustom1.yMax1 = maxQ
        this.lineChartCustom1.yMin1 = minQ
        this.lineChartData1 = res
      }
    },
    chSimId(newVal, oldVal) {
      if (newVal !== oldVal) {
        if (!this.chSimId) return
        this.init()
      }
    },
    currentTime(newVal, oldVal) { // 时间轴改变时，重新获取数据
      if (newVal !== oldVal) {
        if (!this.mapIns || !this.modelData) return
        let { wlevel, q, stakes, maxWlevel, minWlevel, maxQ, minQ, bottom } = getChartsData(this.hedaoName, newVal, this.modelData)
        let res = [
          {
            name: '水位',
            color: '#507EF7',
            yAxisIndex: 0,
            data: wlevel,
          },
          {
            name: '流量',
            color: '#B5E241',
            yAxisIndex: 1,
            data: q,
          },
          {
            name: '渠底高程',
            color: 'gray',
            yAxisIndex: 0,
            data: bottom,
          },
        ]
        this.lineChartCustom1.xAxisData = stakes
        this.lineChartCustom1.yMax0 = maxWlevel
        this.lineChartCustom1.yMin0 = minWlevel
        this.lineChartCustom1.yMax1 = maxQ
        this.lineChartCustom1.yMin1 = minQ
        this.lineChartData1 = res
      }
    }
  },
  created() {
    if (this.chSimId) this.init()
  },

  methods: {
    getTableData() {
      this.hedaoName = this.dataList?.[0]?.projectCode
      this.allData = this.dataList.map((el, index) => {
        const resArr = el.resVOS.map(ele => {
          let obj = {}
          ele.records.forEach(ele => (obj[ele.projectCode] = ele))
          return { ...ele, recordsObj: obj }
        })
        return {
          ...el,
          resVOS: resArr,
          columns: [
            { title: '调度时间', field: 'tm', fixed: 'left', minWidth: 140 },
            ...el.projects.map(ele => ({
              title: ele.projectName,
              children: [
                {
                  field: `recordsObj.${ele.projectCode}.upWlv`,
                  title: '上游水位(m)',
                  minWidth: 100,
                  slots: {
                    default: ({ row, rowIndex }) => {
                      const obj = row.recordsObj[ele.projectCode]
                      if (!obj) return ''
                      return obj.type === 3 ? '-' : obj.upWlv
                    },
                  },
                },
                {
                  field: `recordsObj.${ele.projectCode}.downWlv`,
                  title: '下游水位(m)',
                  minWidth: 100,
                  slots: {
                    default: ({ row, rowIndex }) => {
                      const obj = row.recordsObj[ele.projectCode]
                      if (!obj) return ''
                      return obj.type === 3 ? '-' : obj.downWlv
                    },
                  },
                },
                {
                  title: '流量（m³/s）',
                  minWidth: 170,
                  slots: {
                    default: ({ row, rowIndex }) => {
                      const obj = row.recordsObj[ele.projectCode]
                      if (!obj) return ''
                      return obj.flow
                      // if (obj.type === 3) return '-'

                      // if (obj.type === 0 || obj.type === 2) {
                      //   return (
                      //     <div class='cell-box'>
                      //       <span>闸流量:{obj.outFlow}</span>&nbsp;&nbsp;
                      //       <span>开度:{obj.open}</span>
                      //     </div>
                      //   )
                      // }

                      // if (obj.type === 1 || obj.type === 4) {
                      //   return <span>泵流量:{obj.inFlow}</span>
                      // }
                    },
                  },
                },
              ],
            })),
          ],
        }
      })
    },
    async init() {
      this.spinning = true
      // 根据模型ID获取右上角表格数据
      await getInferRes({ chSimId: parseInt(this.chSimId) })
        .then(res => {
          if (res.data.length > 0) {
            res.data.forEach(element => {
              element['projects'] = element['resVOS'][0].records
            })
            this.initTimesAndData(res.data)
          }
        })

      await axios.get(`${process.env.VUE_APP_MODEL_DATA}/${this.modelId}.json`).then(res => {
        this.modelData = res.data
        extractData(res.data) // 初始化不同水渠中的最大值与最小值
      }).catch(err => {
        this.$message.warning('方案结果请求失败，请联系管理员！')
      }).finally(() => {
        this.spinning = false
      })
      this.onTimeChange(this.currentTime)
      // 初始化右侧表格
      this.getTableData()
    },
    initTimesAndData(scaleData) {
      this.dataList = scaleData
      // 初始化水闸数据
      let tempArr = []
      this.dataList.forEach(el => {
        el.projects.forEach(ele => {
          tempArr.push({ ...ele })
        })
      })
      this.shuizhaGeojsonData = {
        type: 'FeatureCollection',
        features: tempArr.map(el => {
          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [+el.longitude, +el.latitude],
            },
            properties: {
              ...el,
            },
          }
        }),
      }
      if (this.mapIns) {
        this.mapIns.addSource('shuizhaSource', {
          type: 'geojson',
          data: this.shuizhaGeojsonData
        });
        this.mapIns.addLayer({
          id: 'geojson-layer-points',
          type: 'circle',
          source: "shuizhaSource",
          paint: {
            'circle-radius': 5,
            'circle-color': '#12E952',
            'circle-stroke-width': 1,
            'circle-stroke-color': '#fff',
          },
        })
        this.mapIns.addLayer({
          id: 'geojson-layer-points1',
          type: 'symbol',
          source: "shuizhaSource",
          layout: {
            'text-field': ['get', 'projectName'],
            'text-size': 10,
            'text-justify': 'right',
            'text-offset': [0.5, 0.5],
            'text-anchor': 'left',
          },
          paint: {
            'text-color': '#000',
            'text-halo-blur': 1,
            'text-halo-color': '#fff',
            'text-halo-width': 1.5,
          },
        })
        this.mapIns.addInteraction('geojson-layer-shuizha-click-interaction', {
          type: 'click',
          target: { layerId: 'geojson-layer-points' },
          handler: e => {
            const item = e.feature.properties
            getChSimResList({ chSimId: this.chSimId, projectCode: item.projectCode }).then(res => {
              let times = res.data.map(el => el.tm)
              let downWlv = []
              let q = []
              let upWlv = []
              let curdownWlv = ''
              let curupWlv = ''
              let curq = ''
              res.data.forEach((element, index) => {
                downWlv.push(element?.downWlv)
                q.push(+(element?.q))
                upWlv.push(+(element?.upWlv))
                if (element?.tm == this.currentTime) {
                  curdownWlv = element?.downWlv
                  curupWlv = element?.upWlv
                  curq = element?.q
                }
              });

              let chartData = [{
                name: '上游水位',
                color: '#507EF7',
                yAxisIndex: 0,
                data: upWlv
              },
              {
                name: '过闸流量',
                color: '#F7BA1E',
                yAxisIndex: 1,
                data: q
              },
              {
                name: '下游水位',
                color: '#B5E241',
                yAxisIndex: 0,
                data: downWlv
              }]
              this.lineChartCustomShuiZha.xAxisData = times
              this.lineChartCustomShuiZha.yMax0 = Math.max(...upWlv, ...downWlv)
              this.lineChartCustomShuiZha.yMin0 = Math.min(...upWlv, ...downWlv)
              this.lineChartCustomShuiZha.yMax1 = Math.max(...q)
              this.lineChartCustomShuiZha.yMin1 = Math.min(...q)
              this.lineChartDataShuiZha = chartData

              this.activeProcess = { downWlv: curdownWlv || 0, upWlv: curupWlv || 0, q: curq || 0, projectName: item.projectName }
            })
          },
        })
      }
      // 初始化时间轴
      this.times = [...new Set(this.dataList[0].resVOS.map(el => el.tm))]
    },
    onMapMounted(mapIns) {
      this.mapIns = mapIns
      this.$nextTick(() => {
        this.mapIns.resize()
      })
      this.dealLayers()
    },
    goToComingWaterForecast() {
      this.$router.push('/schedule/simulation-model-case')
    },
    onTimeChange(time) {
      this.currentTime = time
    },
    //添加以上两个图层之后 再添加这个函数，参数是动态线的图层配置
    addDashLayer(sourceLayerConfig) {
      const self = this
      const dashArraySequence = [
        [0, 4, 3],
        [0.5, 4, 2.5],
        [1, 4, 2],
        [1.5, 4, 1.5],
        [2, 4, 1],
        [2.5, 4, 0.5],
        [3, 4, 0],
        [0, 0.5, 3, 3.5],
        [0, 1, 3, 3],
        [0, 1.5, 3, 2.5],
        [0, 2, 3, 2],
        [0, 2.5, 3, 1.5],
        [0, 3, 3, 1],
        [0, 3.5, 3, 0.5],
      ]
      let step = 0
      function animateDashArray(timestamp) {
        // Update line-dasharray using the next value in dashArraySequence. The
        // divisor in the expression `timestamp / 50` controls the animation speed.
        const newStep = parseInt((timestamp / 100) % dashArraySequence.length)
        if (newStep !== step) {
          let layer = self.mapIns.getLayer(sourceLayerConfig.id) //获取图层
          // debugger
          if (layer) {
            self.mapIns.setPaintProperty(sourceLayerConfig.id, 'line-dasharray', dashArraySequence[step])
            step = newStep
          }
        }
        // Request the next frame of the animation.
        requestAnimationFrame(animateDashArray)
      }
      // start the animation
      animateDashArray(0)
    },
    async dealLayers() {
      if (!!this.mapIns) {
        await axios(
          // 灌区的边界
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP004&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo></Or>`,
        ).then(res => {
          console.log("this.mapIns.getLayer('geojson-layer-guanqu')", this.mapIns.getLayer("geojson-layer-guanqu"))
          mapBoundGeo(res.data, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
          this.mapIns.addLayer({
            id: 'geojson-layer-guanqu',
            type: 'line',
            source: {
              type: 'geojson',
              lineMetrics: true,
              data: res.data,
            },
            paint: {
              'line-width': 1,
              'line-opacity': 1,
              'line-color': '#2BBCFF',
            },
          })
        })

        await axios(
          // 水库边界
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=thjgq:HP001&maxFeatures=50&outputFormat=application/json&filter=<PropertyIsEqualTo><PropertyName>object_name</PropertyName><Literal>桃花江水库</Literal></PropertyIsEqualTo>`,
        ).then(res => {
          this.mapIns.addLayer({
            id: 'geojson-layer-shuiku',
            type: 'fill',
            source: {
              type: 'geojson',
              lineMetrics: true,
              data: res.data,
            },
            paint: {
              'fill-color': '#12E952',
            },
          })
        })

        let hoveredPolylineId = null
        await axios(
          // 沟渠
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP005&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>2</Literal></PropertyIsEqualTo></Or>`,
        ).then(res => {
          res.data.features.forEach(el => {
            el.geometry.coordinates.forEach(item => item.reverse())
          })
          this.mapIns.addSource('guanqu-source', {
            type: 'geojson',
            data: res.data,
            generateId: true,
          })
          this.mapIns.addLayer({
            id: 'geojson-layer-gouqu',
            type: 'line',
            source: 'guanqu-source',
            paint: {
              'line-width': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                5,
                ['case', ['==', ['get', 'map_class'], '1'], 3, 2],
              ],
              'line-opacity': 1,
              'line-color': 'rgba(255,255,255,0.5)',
            },
          })
          this.mapIns.addLayer({
            id: 'geojson-layer-gouqu1',
            type: 'line',
            source: 'guanqu-source',
            paint: {
              'line-width': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                5,
                ['case', ['==', ['get', 'map_class'], '1'], 3, 2],
              ],
              'line-opacity': 1,
              'line-color': '#12E952',
            },
          })
          this.addDashLayer({
            id: 'geojson-layer-gouqu1',
          })
          this.mapIns.addInteraction('geojson-layer-gouqu1-click-interaction', {
            type: 'click',
            target: { layerId: 'geojson-layer-gouqu1' },
            handler: e => {
              // Copy coordinates array.
              const coordinates = e.lngLat
              const outerHtml = document.createElement('div')
              outerHtml.className = 'outerPaiShuiLine'
              const description = e.feature.properties.object_name
              outerHtml.textContent = description
              new mapboxgl.Popup().setLngLat(coordinates).setDOMContent(outerHtml).addTo(this.mapIns)
              this.hedaoName = "GQ" + e.feature.properties.id
            },
          })
          this.mapIns.on('mousemove', 'geojson-layer-gouqu1', e => {
            if (e.features.length > 0) {
              if (hoveredPolylineId !== null) {
                this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: false })
              }
              hoveredPolylineId = e.features[0].id
              this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: true })
            }
          })
          this.mapIns.on('mouseleave', 'geojson-layer-gouqu1', () => {
            if (hoveredPolylineId !== null) {
              this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: false })
            }
            hoveredPolylineId = null
          })
        })
      }
    }
  },
}
</script>

<style lang="less" scoped>
.summary {
  display: flex;
  justify-content: space-between;

  .hgroup {
    width: 19%;
    padding: 24px 26px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;

    .content {
      flex: 1;
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .num {
      font-weight: 700;
      font-size: 24px;
      color: #1d2129;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 0;
    }

    .text {
      font-size: 14px;
      color: #4e5969;
      font-weight: 400;
      margin: 0;
    }

    .unit {
      margin-left: -2px;
      font-size: 14px;
    }

    .icon {
      width: 50px;
      height: 50px;
      display: inline-block;
      flex-shrink: 0;
    }

    &:nth-child(1) {
      .icon {
        background: url('@/assets/images/three-days-rain.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(2) {
      .icon {
        background: url('@/assets/images/all-rain.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(3) {
      .icon {
        background: url('@/assets/images/coming-water.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(4) {
      .icon {
        background: url('@/assets/images/flood-peak.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(5) {
      .icon {
        background: url('@/assets/images/flood-peak-time.png') 0 0 no-repeat;
        background-size: 100%;
      }

      .num {
        font-size: 15px;
      }
    }
  }
}

::v-deep .outerPaiShuiLine {
  width: 150px;
  // height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  background-color: rgb(83, 132, 254);
}

.flood-box {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;

  .flood-tabs {
    margin: 0;

    .name {
      font-size: 20px;
      color: #1d2129;
      font-weight: 600;
    }
  }

  .flood-content {
    flex: 1;
  }
}

@font-face {
  font-family: 'AlimamaDaoLiTi';
  src: url('@/assets/font/AlimamaDaoLiTi.ttf');
}

.curve-panel {
  position: absolute;
  z-index: 1000;
  bottom: 4px;
  right: 4px;
  width: 700px;
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  background: #ffffff;

  .left {
    border-right: 1px solid #e5e6eb;
    width: 150px;
    position: relative;
    display: flex;
    flex-direction: column;

    .header {
      background: #f2f3f5;
      font-weight: 600;
      color: #1d2129;
      line-height: 20px;
      padding: 6px 8px;
      display: flex;
      align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }

      .name {
        flex: 1;
        margin: 0 0 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .indicator {
      display: flex;
      padding: 6px 8px;
      justify-content: space-between;

      .label {
        color: '#4E5969';
      }

      .value {
        color: #1d2129;
      }
    }
  }

  .right {
    flex: 1;
    padding-top: 10px;
  }
}
</style>
