<template>
  <VxeTable
    ref="vxeTableRef"
    :columns="columns"
    :tableData="$attrs.dataSource"
    size="small"
    :tablePage="false"
    :isShowTableHeader="false"
  ></VxeTable>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'ResultTable',
    props: ['resultData'],
    components: {
      VxeTable,
    },
    data() {
      return {}
    },
    computed: {
      columns() {
        const baseColumns = [
          {
            title: '预报时间',
            field: 'tm',
            minWidth: 120,
            align: 'center',
            fixed: 'left',
          },
          {
            title: '水位(m)',
            field: 'wlv',
            minWidth: 80,
            maxWidth: 80,
            align: 'center',
          }, 
          {
            title: '库容(万m³)',
            field: 'storage',
            minWidth: 100,
            align: 'center',
          },
          {
            title: '时段雨量(mm/h)',
            field: 'rain',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '入库流量(m³/s)',
            field: 'inflow',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '入库水量(万m³)',
            field: 'inWater',
            minWidth: 120,
            align: 'center',
          },
        ]
        console.log(this.resultData)
        // 当 dispatchType 为 2 时，添加需水流量列
        if (this.resultData?.dispathType === 2) {
          console.log(this.resultData)
          baseColumns.push({
            title: '需水流量(m³/s)',
            field: 'downFlow',
            minWidth: 120,
            align: 'center',
          })
        }

        // 添加剩余的列
        baseColumns.push(
          {
            title: '供水流量(m³/s)',
            field: 'outflow',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '供水量(万m³)',
            field: 'outWater',
            minWidth: 110,
            align: 'center',
          },
          {
            title: '泄洪流量(m³/s)',
            field: 'floodflow',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '泄洪量(万m³)',
            field: 'floodWater',
            minWidth: 110,
            align: 'center',
          }
        )

        return baseColumns
      }
    },
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped></style>
