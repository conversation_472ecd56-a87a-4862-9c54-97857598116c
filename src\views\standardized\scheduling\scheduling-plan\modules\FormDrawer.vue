<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="600"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="控运计划名称" prop="controlName">
              <a-input allowClear v-model="form.controlName" placeholder="请输入" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="年份" prop="year">
              <a-date-picker
                mode="year"
                format="YYYY"
                v-model="form.year"
                placeholder="请选择"
                allow-clear
                :open="yearShowOne"
                style="width: 100%"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
                @keyup.enter.native="handleQuery"
              ></a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="启用时间" prop="enableTime">
              <a-date-picker v-model="form.enableTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="类型" prop="type">
                <a-select v-model="form.type" placeholder="请选择">
                <a-select-option v-for="(d, index) in controlTypeOptions" :key="index" :value="d.key">
                  {{ d.value }}
                </a-select-option>
              </a-select>
              </a-form-model-item>
            </a-col> -->

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="附件" prop="positionAttaches">
              <UploadFile
                :fileUrl.sync="form.positionAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addSchedulingPlan, editSchedulingPlan, getSchedulingPlan } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'FormTemplate',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'controlTypeOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        form: {
          controlName: null,
          year: null,
          enableTime: null,
          type: 1,
          projectId: null,
          positionAttaches: [],
        },
        open: false,
        rules: {
          controlName: [{ required: true, message: '控运计划名称不能为空', trigger: 'blur' }],
          year: [{ required: true, message: '年份不能为空', trigger: 'change' }],
          enableTime: [{ required: true, message: '启用时间不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
          positionAttaches: [{ required: true, message: '附件不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      // 获取文件
      getFileUrl(url) {
        this.form.projectImgUrl = url
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getSchedulingPlan({ controlPlanId: row.controlPlanId }).then(res => {
            this.form = {
              ...res.data,
              positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
            }
            console.log('* 详情 附件', this.form.positionAttaches, typeof this.form.positionAttaches)
            this.form.year = moment(`${res.data.year}`)
            this.modalLoading = false
          })
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.enableTime = moment(this.form.enableTime).format('YYYY-MM-DD')
            this.form.year = parseInt(this.form.year)
            if (this.form.controlPlanId == null) {
              addSchedulingPlan(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editSchedulingPlan(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
</style>
