<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff; padding: 24px">
    <!-- 第一大区块 -->
    <div
      style="display: flex;justify-content: space-between;align-items: center;padding-bottom: 16px;border-bottom: 1px solid #f2f3f5; height: 0.4rem;">
      <div style="font-size: 0.2rem; font-weight: 600;"> {{ nowTime }} </div>
      <div>
        <a-button type="primary" style="color: #fff; background: #165dff; font-size: 14px; font-weight: 400"
          @click="goToComingWaterForecast">
          方案管理
        </a-button>
      </div>
    </div>

    <!-- 内容 -->
    <div style="position: relative; display: flex; flex-direction: row; height: calc(100% - 0.4rem);width: 100%;">
      <!-- 左侧地图区域 -->
      <div style="flex: 1; display: flex; flex-direction: column">
        <MapBox @onMapMounted="onMapMounted" />
      </div>

      <!-- 右侧区域 -->
      <div style="flex:1; height: 100%; display: flex; flex-direction: column">
        <div style="flex:3;height: 100%; padding: 0.04rem; position: relative">
          <VxeTable ref="flowTableRef" size="small" :columns="columns" :tableData="flowData" :tablePage="false"
            :isShowTableHeader="false" />
        </div>
        <!-- 折线图区域 -->
        <div style="flex:1;height: 100%; padding: 0.02rem; position: relative;margin-top: 0.1rem;">
          <div style="position: absolute; top: 0.01rem; left: 1.5rem; width: 2rem; height: 0.3rem; z-index: 1000">
            <a-select v-model="hedaoName" allowClear
              style="width: 100%; height: 0.25rem; font-weight: 400; font-size: 0.12rem" placeholder="请选择"
              :options="hedaoOptions" show-search></a-select>
          </div>
          <LineEchart :height="'200px'" :width="'800px'" style="margin-top: 0.1rem" :dataSource="lineChartData"
            ref="modelLineChartRef" :custom="lineChartCustom"></LineEchart>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MapBox from './component/MapBox/index.vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import * as turf from '@turf/turf'
import { mapboxShuiZhaLabelPopup } from './modules/AddModal/component/popup.js'
import VxeTable from '@/components/VxeTable/index.vue'
import { LineEchart } from '@/components/Echarts'
import { getChWaterList } from './services'
import axios from 'axios'
import { mapBoundGeo } from '@/utils/mapBounds.js'
let showPopupItem = [] // 存储已显示的弹窗

export default {
  name: 'ManualForecast',
  data() {
    return {
      nowTime: "",
      loading: false,
      // 流量数据
      flowData: [],
      columns: [{
        field: 'dispatchObject',
        title: '监测站',
        width: '34%',
        align: 'center',
      },
      {
        field: 'waterLevel',
        title: '水位(m)',
        width: '33%',
        align: 'center'
      },
      {
        field: 'flow',
        title: '流量(m³/s)',
        width: '33%',
        align: 'center'
      }],

      // 折线图数据
      chartData: {
        dataSource: [
          {
            name: '水位',
            color: '#1890ff', // 蓝色
            data: [], // 格式: [['时间', 水位值], ['时间', 水位值], ...]
          },
        ],
        custom: {
          shortValue: false,
          dataZoom: false,
          showAreaStyle: false,
          xLabel: 'm',
          yLabel: '水位(m)',
          legend: false,
          grid: {
            left: '10%',
            right: '10%',
            top: '15%',
            bottom: '15%',
            containLabel: true,
          },
        },
      },

      // 渠道断面图数据
      lineChartData: [],
      lineChartCustom: {
        shortValue: true, // 缩写坐标值
        xLabel: '里程/m', // x轴名称
        yLabel: '实时水位(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        // rYUnit: '', // 右侧y轴单位
        // rYLabel: '流量(m³/s)', // 右侧y轴名称
        dataZoom: false,
        color: null,
        grid: {
          left: '1%',
          right: '0%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendRight: '5%',
        xAxisData: [],
      },

      // 河道下拉列表
      hedaoName: '',
      hedaoOptions: [],
      // 河道所有横断面数据
      hedaoChartData: {},
    }
  },
  components: {
    VxeTable,
    LineEchart,
    MapBox,
  },
  mapIns: null,
  async created() {
    // 更新时间的间隔为1秒，根据需要调整
    setInterval(() => {
      this.nowTime = new Date().toLocaleString().replace("T", " ").replaceAll("/", "-")
    }, 1000)
    // 数据默认 5 分钟刷新一次 
    setInterval(() => {
      this.loadWaterFlowData()
      console.log('数据默认 5 分钟刷新一次')
    }, 1000 * 5)
  },
  watch: {
    hedaoName: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          if (Object.keys(this.hedaoChartData).length > 0 && this.hedaoChartData[newVal]) {
            let { wlv, mileage } = this.hedaoChartData[this.hedaoName]
            this.lineChartCustom.xAxisData = mileage
            this.lineChartCustom.yMax0 = Math.max(...wlv,)
            this.lineChartCustom.yMin0 = Math.min(...wlv,)
            this.lineChartData = [
              {
                name: '实时水位',
                color: '#507EF7',
                yAxisIndex: 0,
                data: wlv,
              }
            ]
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    goToComingWaterForecast() {
      this.$router.push('/schedule/simulation-model-case')
    },
    // 加载水位流量数据
    async loadWaterFlowData() {
      try {
        const response = await getChWaterList()
        if (response.data) {
          const riverData = response.data || []
          // 处理水位和流量数据
          let flowData = []
          // 用于存储水闸的坐标与属性信息
          let shuiZhaPoints = []
          // 整理渠道横断面数据
          let hedaoChartDataTemp = {}
          // 遍历所有河道数据
          riverData.forEach(river => {
            // 处理每个河道的站点数据转换为echarts可以使用的数据
            let tempObj = (hedaoChartDataTemp[river.projectCode] = {})
            tempObj['code'] = river.projectCode
            tempObj['name'] = river.projectName
            tempObj['wlv'] = []
            tempObj['flow'] = []
            tempObj['elevation'] = []
            tempObj['mileage'] = []

            if (river.sites && river.sites.length > 0) {
              river.sites.forEach(site => {
                // 流量数据
                flowData.push({
                  dispatchObject: site.siteName,
                  flow: site.flow,
                  waterLevel: site.wlv,
                })
                // 渠道横断面数据
                tempObj['wlv'].push(+site.wlv)
                tempObj['mileage'].push(+site.mileage)
                tempObj['flow'].push(+site.flow)
                tempObj['elevation'].push(+site.elevation)

                // 水闸所有属性信息
                shuiZhaPoints.push({
                  ...site,
                  lineName: river.projectName,
                  lineCode: river.projectCode,
                  projectName: site.siteName,
                  projectCode: site.siteCode,
                })
              })
            }
            if (tempObj['mileage'].length && tempObj['mileage'][0] != 0) {
              tempObj['wlv'].unshift(tempObj['wlv'][0])
              tempObj['flow'].unshift(tempObj['flow'][0])
              tempObj['elevation'].push(tempObj['elevation'][0])
              tempObj['mileage'].unshift(0)
            }
            if (tempObj['mileage'].length && (tempObj['mileage'][tempObj['mileage'].length - 1] != river.endMileage)) {
              tempObj['wlv'].push(tempObj['wlv'][tempObj['wlv'].length - 1])
              tempObj['flow'].push(tempObj['flow'][tempObj['flow'].length - 1])
              tempObj['elevation'].push(tempObj['elevation'][tempObj['elevation'].length - 1])
              tempObj['mileage'].push(river.endMileage)
            }
            tempObj['wlv'] = tempObj['wlv'].map(el => Number(el).toFixed(2))
            tempObj['flow'] = tempObj['flow'].map(el => Number(el).toFixed(2))
            tempObj['elevation'] = tempObj['elevation'].map(el => Number(el).toFixed(2))
          })
          this.hedaoOptions = Object.keys(hedaoChartDataTemp).map(key => ({
            label: hedaoChartDataTemp[key].name,
            value: hedaoChartDataTemp[key].code,
          }))
          if (this.hedaoOptions.length > 1) this.hedaoName = this.hedaoOptions[1].value
          // 设置表格数据
          this.flowData = flowData.map((item, index) => {
            item.flow = Number(item.flow).toFixed(2), // 保留两位小数
              item.waterLevel = Number(item.waterLevel).toFixed(2)
            return item
          })

          // 组合成为 GeoJSON 格式
          this.shuiZhaGeojson = {
            type: 'FeatureCollection',
            features: shuiZhaPoints.map(el => {
              return {
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+el.longitude, +el.latitude],
                },
                properties: {
                  ...el,
                },
              }
            }),
          }
          if (this.mapIns) {
            this.mapIns.getSource('shuizhaSource') && this.mapIns.getSource('shuizhaSource').setData(this.shuiZhaGeojson)
          }
          // 渠道数据
          this.hedaoChartData = hedaoChartDataTemp
        }
      } catch (error) {
        this.$message.error('获取水位流量数据失败')
      }
    },

    async onMapMounted(mapIns) {
      await this.loadWaterFlowData()
      this.mapIns = mapIns
      this.$nextTick(() => {
        this.mapIns.resize()
      })
      this.dealLayers()
    },
    async dealLayers() {
      if (!!this.mapIns && !!this.shuiZhaGeojson) {
        await axios(
          // 灌区的边界
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP004&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo></Or>`,
        ).then(res => {
          mapBoundGeo(res.data, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
          this.mapIns.addLayer({
            id: 'geojson-layer-guanqu',
            type: 'line',
            source: {
              type: 'geojson',
              lineMetrics: true,
              data: res.data,
            },
            paint: {
              'line-width': 1,
              'line-opacity': 1,
              'line-color': '#2BBCFF',
            },
          })
        })
        let hoveredPolylineId = null
        await axios(
          // 沟渠
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP005&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>2</Literal></PropertyIsEqualTo></Or>`,
        ).then(res => {
          res.data.features.forEach(el => {
            el.geometry.coordinates.forEach(item => item.reverse())
          })
          this.mapIns.addSource('guanqu-source', {
            type: 'geojson',
            data: res.data,
            generateId: true,
          })
          this.mapIns.addLayer({
            id: 'geojson-layer-gouqu',
            type: 'line',
            source: 'guanqu-source',
            paint: {
              'line-width': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                5,
                ['case', ['==', ['get', 'map_class'], '1'], 3, 2],
              ],
              'line-opacity': 1,
              'line-color': 'rgba(255,255,255,0.5)',
            },
          })
          this.mapIns.addLayer({
            id: 'geojson-layer-gouqu1',
            type: 'line',
            source: 'guanqu-source',
            paint: {
              'line-width': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                5,
                ['case', ['==', ['get', 'map_class'], '1'], 3, 2],
              ],
              'line-opacity': 1,
              'line-color': '#12E952',
            },
          })
          this.addDashLayer({
            id: 'geojson-layer-gouqu1',
          })
          this.mapIns.addInteraction('geojson-layer-gouqu1-click-interaction', {
            type: 'click',
            target: { layerId: 'geojson-layer-gouqu1' },
            handler: e => {
              // Copy coordinates array.
              const coordinates = e.lngLat
              const outerHtml = document.createElement('div')
              outerHtml.className = 'outerPaiShuiLine'
              const description = e.feature.properties.object_name
              this.hedaoName = this.hedaoOptions.find(item => item.label == description).value
              outerHtml.textContent = description
              // this.extractData(e.feature.properties.id)
              new mapboxgl.Popup().setLngLat(coordinates).setDOMContent(outerHtml).addTo(this.mapIns)
            },
          })
          this.mapIns.on('mousemove', 'geojson-layer-gouqu1', e => {
            if (e.features.length > 0) {
              if (hoveredPolylineId !== null) {
                this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: false })
              }
              hoveredPolylineId = e.features[0].id
              this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: true })
            }
          })
          this.mapIns.on('mouseleave', 'geojson-layer-gouqu1', () => {
            if (hoveredPolylineId !== null) {
              this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: false })
            }
            hoveredPolylineId = null
          })
        })
        await axios(
          // 水库边界
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=thjgq:HP001&maxFeatures=50&outputFormat=application/json&filter=<PropertyIsEqualTo><PropertyName>object_name</PropertyName><Literal>桃花江水库</Literal></PropertyIsEqualTo>`,
        ).then(res => {
          this.mapIns.addLayer({
            id: 'geojson-layer-shuiku',
            type: 'fill',
            source: {
              type: 'geojson',
              lineMetrics: true,
              data: res.data,
            },
            paint: {
              'fill-color': '#12E952',
            },
          })
          this.mapIns.addSource('shuizhaSource', {
            type: 'geojson',
            data: this.shuiZhaGeojson
          });
          this.mapIns.addLayer({
            id: 'geojson-layer-points',
            type: 'circle',
            source: "shuizhaSource",
            paint: {
              'circle-radius': 5,
              'circle-color': '#12E952',
              'circle-stroke-width': 1,
              'circle-stroke-color': '#fff',
            },
          })
          this.mapIns.addLayer({
            id: 'geojson-layer-points1',
            type: 'symbol',
            source: "shuizhaSource",
            layout: {
              'text-field': ['get', 'siteName'],
              'text-size': 10,
              'text-justify': 'right',
              'text-offset': [0.5, 0.5],
              'text-anchor': 'left',
            },
            paint: {
              'text-color': '#000',
              'text-halo-blur': 1,
              'text-halo-color': '#fff',
              'text-halo-width': 1.5,
            },
          })
          this.mapIns.addInteraction('geojson-layer-shuizha-click-interaction', {
            type: 'click',
            target: { layerId: 'geojson-layer-points' },
            handler: e => {
              const coordinates = e.lngLat
              let index = showPopupItem.findIndex(el => el.siteCode == e.feature.properties.siteCode)
              if (index != -1) return
              let curr = e.feature.properties
              const popupIns = mapboxShuiZhaLabelPopup(this.mapIns, {
                ...curr,
                lngLat: coordinates,
                onPopupClose: item => {
                  const index = showPopupItem.findIndex(el => el.siteCode == item.siteCode)
                  if (index === -1) return
                  showPopupItem[index].popupIns.remove()
                  showPopupItem = showPopupItem.filter((el, i) => i !== index)
                },
                onProcessClick: item => {
                  this.waterLevelData = this.waterLevelData.map(el => {
                    if (el.dispatchObject == item.siteName) {
                      el.waterLevel = item.wlv
                    }
                    return el
                  })
                  this.flowData = this.flowData.map(el => {
                    if (el.dispatchObject == item.siteName) {
                      el.flow = item.flow
                    }
                    return el
                  })
                },
              })
              showPopupItem.push({ ...curr, popupIns })
            },
          })
        })
      }
    },
    //添加以上两个图层之后 再添加这个函数，参数是动态线的图层配置
    addDashLayer(sourceLayerConfig) {
      const self = this
      const dashArraySequence = [
        [0, 4, 3],
        [0.5, 4, 2.5],
        [1, 4, 2],
        [1.5, 4, 1.5],
        [2, 4, 1],
        [2.5, 4, 0.5],
        [3, 4, 0],
        [0, 0.5, 3, 3.5],
        [0, 1, 3, 3],
        [0, 1.5, 3, 2.5],
        [0, 2, 3, 2],
        [0, 2.5, 3, 1.5],
        [0, 3, 3, 1],
        [0, 3.5, 3, 0.5],
      ]
      let step = 0
      function animateDashArray(timestamp) {
        // Update line-dasharray using the next value in dashArraySequence. The
        // divisor in the expression `timestamp / 50` controls the animation speed.
        const newStep = parseInt((timestamp / 100) % dashArraySequence.length)
        if (newStep !== step) {
          let layer = self.mapIns.getLayer(sourceLayerConfig.id) //获取图层
          // debugger
          if (layer) {
            self.mapIns.setPaintProperty(sourceLayerConfig.id, 'line-dasharray', dashArraySequence[step])
            step = newStep
          }
        }
        // Request the next frame of the animation.
        requestAnimationFrame(animateDashArray)
      }
      // start the animation
      animateDashArray(0)
    },
  }
}
</script>

<style lang="less" scoped>
::v-deep .outerPaiShuiLine {
  width: 150px;
  // height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  background-color: rgb(83, 132, 254);
}

::v-deep .mapboxgl-popup-content {
  padding: 0;
}
</style>
