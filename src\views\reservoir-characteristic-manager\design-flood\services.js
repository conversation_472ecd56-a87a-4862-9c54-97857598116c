import request from '@/utils/request'

// 分页查询设计洪水（含细节）
export function getCstDesignFloodPage() {
  return request({
    url: '/custom/cstDesignFlood/page',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//查看设计洪水详情（含细节）
export function getCstDesignFloodDetails(params) {
  return request({
    url: `/custom/cstDesignFlood/${params.id}`,
    method: 'get',
    headers: {
      'Content-Type': 'x-www-form-urlencoded',
    },
  })
}

//新增设计洪水（含细节）
export function addCstDesignFlood(data) {
  return request({
    url: '/custom/cstDesignFlood',
    method: 'post',
    data,
  })
}
//新增设计洪水（含细节）
export function updateCstDesignFlood(data) {
  return request({
    url: `/custom/cstDesignFlood/${data.id}`,
    method: 'post',
    data,
  })
}

//删除设计洪水
export function delCstDesignFlood(params) {
  return request({
    url: `/custom/cstDesignFlood/${params.id}`,
    method: 'DELETE',
    headers: {
      'Content-Type': 'x-www-form-urlencoded',
    },
  })
}

//设计洪水过程
// 分页查询设计洪水过程（含细节）
export function getCstDesignFloodDurationPage() {
  return request({
    url: '/custom/cstDesignFloodDuration/page',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//查看设计洪水过程详情（含细节）
export function getCstDesignFloodDurationDetails(params) {
  return request({
    url: `/custom/cstDesignFloodDuration/${params.id}`,
    method: 'get',
    headers: {
      'Content-Type': 'x-www-form-urlencoded',
    },
  })
}

//新增设计洪水过程（含细节）
export function addCstDesignFloodDuration(data) {
  return request({
    url: '/custom/cstDesignFloodDuration',
    method: 'post',
    data,
  })
}
//新增设计洪水过程（含细节）
export function updateCstDesignFloodDuration(data) {
  return request({
    url: `/custom/cstDesignFloodDuration/${data.id}`,
    method: 'post',
    data,
  })
}

//删除设计洪水过程
export function delCstDesignFloodDuration(params) {
  return request({
    url: `/custom/cstDesignFloodDuration/${params.id}`,
    method: 'DELETE',
    headers: {
      'Content-Type': 'x-www-form-urlencoded',
    },
  })
}
//上传
export function uploadCstDesignFloodDuration(data) {
  return request({
    url: `/custom/cstDesignFloodDuration/upload`,
    method: 'post',
    data,
  })
}
