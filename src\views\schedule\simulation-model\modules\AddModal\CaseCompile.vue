<template>
  <div class="case-compile-container">
    <!-- <div class="page-title">仿真方案编制</div> -->

    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section" style="display: flex; flex-direction: column">
        <!-- 地图区域 -->
        <div style="flex: 4; padding: 0.02rem; background: #fafafa">
          <MapBox @onMapMounted="onMapMounted" />
        </div>

        <!-- 折线图区域 -->
        <div style="flex: 1; padding: 0.02rem; height: 100%; width: 100%; position: relative">
          <div style="position: absolute; top: 0.01rem; left: 1.5rem; width: 2rem; height: 0.3rem; z-index: 1000">
            <a-select v-model="hedaoName" allowClear
              style="width: 100%; height: 0.25rem; font-weight: 400; font-size: 0.12rem" placeholder="请选择"
              :options="hedaoOptions" show-search></a-select>
          </div>
          <LineEchart :height="'200px'" :width="'800px'" style="margin-top: 0.1rem" :dataSource="lineChartData"
            ref="modelLineChartRef" :custom="lineChartCustom"></LineEchart>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 供需水表格 -->
        <div class="supply-demand-section">
          <div class="section-header">
            <div class="header-left">
              <div class="section-title">供需水</div>
            </div>
            <div class="header-right">
              <a-button type="primary" @click="handleSupplyDemandBatchImport" class="batch-import-btn">
                批量导入
              </a-button>
            </div>
          </div>
          <div class="water-inputs">
            <div class="supply-input">
              <label>可供水量：</label>
              <a-input-number v-model="supplyDemandData.actualSupply" :min="0" :step="0.01" placeholder="请输入"
                style="width: 150px; margin-left: 8px" />
              <span style="margin-left: 4px">万m³</span>
            </div>
            <div class="total-demand-input">
              <label>总需水量：</label>
              <a-input-number :value="totalDemandWater" :min="0" :step="0.01" placeholder="自动计算"
                style="width: 150px; margin-left: 8px" :disabled="true" />
              <span style="margin-left: 4px">万m³</span>
            </div>
          </div>
          <div class="table-container">
            <VxeTable ref="supplyDemandTableRef" size="small" :columns="supplyDemandColumns"
              :tableData="supplyDemandData.tableData" :tablePage="false" :isShowTableHeader="false" />
          </div>
        </div>

        <!-- 下方两个表格 -->
        <div class="bottom-tables">
          <!-- 水位表格 -->
          <div class="water-level-section">
            <div class="section-header">
              <div class="section-title">水位</div>
              <a-button type="primary" @click="handleWaterLevelBatchImport" class="batch-import-btn">批量导入</a-button>
            </div>
            <div class="table-container">
              <VxeTable ref="waterLevelTableRef" size="small" :columns="waterLevelColumns" :tableData="waterLevelData"
                :tablePage="false" :isShowTableHeader="false" />
            </div>
          </div>

          <!-- 流量表格 -->
          <div class="flow-section">
            <div class="section-header">
              <div class="section-title">流量</div>
              <a-button type="primary" @click="handleFlowBatchImport" class="batch-import-btn">批量导入</a-button>
            </div>
            <div class="table-container">
              <VxeTable ref="flowTableRef" size="small" :columns="flowColumns" :tableData="flowData" :tablePage="false"
                :isShowTableHeader="false" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量导入弹窗 -->
    <SimulationBatchImportModal :visible.sync="batchImportModal.visible" :nameList="batchImportModal.nameList"
      :sourceType="batchImportModal.sourceType" @save="handleBatchImportSave" />
  </div>
</template>

<script lang="jsx">
import moment from 'moment'
import MapBox from '../../component/MapBox/index.vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import * as turf from '@turf/turf'
import { mapboxShuiZhaPopup, mapboxShuikuPopup } from './component/popup.js'
import VxeTable from '@/components/VxeTable/index.vue'
import LineEchart from '@/views/schedule/simulation-model/component/LineEchart.vue'
import { getChDeptFlow, forecast, getInWaterPage, getChWaterList, getMaxOutWater } from '../../services'
import SimulationBatchImportModal from './SimulationBatchImportModal.vue'
import axios from 'axios'
import { mapBoundGeo } from '@/utils/mapBounds.js'
let showPopupItem = [] // 存储已显示的弹窗
export default {
  name: 'CaseCompile',
  props: ['baseInfo', 'projectFlows', 'inWaterEchoData'],
  mapIns: null,
  components: {
    VxeTable,
    LineEchart,
    SimulationBatchImportModal,
    MapBox,
  },
  data() {
    return {
      // 原有数据
      dispatchCaseOptions: {},
      dispatchCase: '',
      tableKey: 1,
      loading: false,
      allData: [],
      active: undefined,
      tableData: [],
      tableColumns: [],

      // 来水方案数据
      maxOutWaterData: null, // 存储getMaxOutWater接口返回的data
      isLoadingWaterData: false, // 是否正在加载来水方案数据

      // 悬浮状态
      hoveredRowIndex: -1,
      hoveredField: '',
      hoveredTableType: '', // 'supplyDemand', 'waterLevel', 'flow'

      // 批量导入弹窗
      batchImportModal: {
        visible: false,
        nameList: [],
        sourceType: '', // 'waterDemandValue', 'waterLevel', 'flow'
      },

      // 供需水数据
      supplyDemandData: {
        actualSupply: undefined, // 实际可供水量
        tableData: [], // 需水口数据
        originalRecords: [], // 保存原始的需水口完整数据
      },

      // 水位数据
      waterLevelData: [],

      // 流量数据
      flowData: [],

      // 折线图数据
      chartData: {
        dataSource: [
          {
            name: '水位',
            color: '#1890ff', // 蓝色
            data: [], // 格式: [['时间', 水位值], ['时间', 水位值], ...]
          },
        ],
        custom: {
          shortValue: false,
          dataZoom: false,
          showAreaStyle: false,
          xLabel: 'm',
          yLabel: '水位(m)',
          legend: false,
          grid: {
            left: '10%',
            right: '10%',
            top: '15%',
            bottom: '15%',
            containLabel: true,
          },
        },
      },

      // 渠道断面图数据
      lineChartData: [],
      lineChartCustom: {
        shortValue: true, // 缩写坐标值
        xLabel: '里程/m', // x轴名称
        yLabel: '实时水位(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        // rYUnit: '', // 右侧y轴单位
        // rYLabel: '流量(m³/s)', // 右侧y轴名称
        dataZoom: false, // 数据缩放,
        color: null,
        grid: {
          left: '1%',
          right: '0%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '65%',
        xAxisData: [],
      },

      // 河道下拉列表
      hedaoName: '',
      hedaoOptions: [],
      // 河道所有横断面数据
      hedaoChartData: {},
      
      // 防抖相关
      saveDebounceTimer: null,
      isSaving: false,
    }
  },
  computed: {
    // 计算总需水量
    totalDemandWater() {
      if (!this.supplyDemandData.tableData || this.supplyDemandData.tableData.length === 0) {
        return 0
      }
      return this.supplyDemandData.tableData.reduce((total, item) => {
        return total + (Number(item.waterDemandValue) || 0)
      }, 0)
    },
    // 供需水表格列配置
    supplyDemandColumns() {
      return [
        {
          field: 'waterIntakeName',
          title: '需水口',
          width: '50%',
          align: 'center',
        },
        {
          field: 'waterDemandValue',
          title: '需水量(万m³)',
          width: '50%',
          align: 'center',
          slots: {
            default: ({ row, rowIndex }) => {
              return this.renderEditableCell(row, rowIndex, 'waterDemandValue', 'supplyDemand')
            },
          },
        },
      ]
    },
    // 水位表格列配置
    waterLevelColumns() {
      return [
        {
          field: 'dispatchObject',
          title: '调度对象',
          width: '50%',
          align: 'left',
        },
        {
          field: 'waterLevel',
          title: '水位(m)',
          width: '50%',
          align: 'left',
          slots: {
            default: ({ row, rowIndex }) => {
              return this.renderEditableCell(row, rowIndex, 'waterLevel', 'waterLevel')
            },
          },
        },
      ]
    },
    // 流量表格列配置
    flowColumns() {
      return [
        {
          field: 'dispatchObject',
          title: '调度对象',
          width: '50%',
          align: 'center',
        },
        {
          field: 'flow',
          title: '流量(m³/s)',
          width: '50%',
          align: 'center',
          slots: {
            default: ({ row, rowIndex }) => {
              return this.renderEditableCell(row, rowIndex, 'flow', 'flow')
            },
          },
        },
      ]
    },
  },
  watch: {
    baseInfo: {
      handler(newVal) {
        if (newVal && newVal.startTime && newVal.endTime && newVal.simulateType && !this.inWaterEchoData) {
          this.loadSupplyDemandData()
          this.loadWaterFlowData()
        }
      },
      immediate: true,
      deep: true,
    },
    inWaterEchoData: {
      handler(newVal) {
        if (newVal) {
          // 加载复制的供需水数据
          // 设置实际可供水量
          this.supplyDemandData.actualSupply = newVal.chDeptFlows.supplyWaterValue

          // 保存原始的完整数据
          this.supplyDemandData.originalRecords = newVal.chDeptFlows.records || []

          // 设置需水口数据
          this.supplyDemandData.tableData = (newVal.chDeptFlows.records || []).map(item => ({
            waterIntakeName: item.deptName, // 需水口名称
            waterDemandValue: item.waterDemandValue, // 需水量(万m³)
          }))
          this.maxOutWaterData = newVal.outWaters

          // 加载复制的水位流量数据
          const riverData = newVal?.chSiteWaters || []
          // 处理水位和流量数据
          let waterLevelData = []
          let flowData = []
          let chartData = []
          // 用于存储水闸的坐标与属性信息
          let shuiZhaPoints = []
          // 整理渠道横断面数据
          let hedaoChartDataTemp = {}
          // 遍历所有河道数据
          riverData.forEach(river => {
            // 处理每个河道的站点数据转换为echarts可以使用的数据
            let tempObj = (hedaoChartDataTemp[river.projectCode] = {})
            tempObj['code'] = river.projectCode
            tempObj['name'] = river.projectName
            tempObj['wlv'] = []
            tempObj['flow'] = []
            tempObj['mileage'] = []

            if (river.sites && river.sites.length > 0) {
              river.sites.forEach(site => {
                // 水位数据
                waterLevelData.push({
                  dispatchObject: site.siteName,
                  waterLevel: site.wlv,
                })

                // 流量数据
                flowData.push({
                  dispatchObject: site.siteName,
                  flow: site.flow,
                })

                // 渠道横断面数据
                tempObj['wlv'].push(+site.wlv)
                tempObj['mileage'].push(+site.mileage)
                tempObj['flow'].push(+site.flow)

                // 水闸所有属性信息
                shuiZhaPoints.push({
                  ...site,
                  lineName: river.projectName,
                  lineCode: river.projectCode,
                  projectName: site.siteName,
                  projectCode: site.siteCode,
                })
              })
            }

            if (tempObj['mileage'].length && tempObj['mileage'][0] != 0) {
              tempObj['wlv'].unshift(tempObj['wlv'][0])
              tempObj['flow'].unshift(tempObj['flow'][0])
              tempObj['mileage'].unshift(0)
            }
            if (tempObj['mileage'].length && (tempObj['mileage'][tempObj['mileage'].length - 1] != river.endMileage)) {
              tempObj['wlv'].push(tempObj['wlv'][tempObj['wlv'].length - 1])
              tempObj['flow'].push(tempObj['flow'][tempObj['flow'].length - 1])
              tempObj['mileage'].push(river.endMileage)
            }
            tempObj['wlv'] = tempObj['wlv'].map(el => Number(el).toFixed(2))
            tempObj['flow'] = tempObj['flow'].map(el => Number(el).toFixed(2))
            tempObj['mileage'] = tempObj['mileage'].map(el => Number(el).toFixed(2))
          })
          this.hedaoOptions = Object.keys(hedaoChartDataTemp).map(key => ({
            label: hedaoChartDataTemp[key].name,
            value: hedaoChartDataTemp[key].code,
          }))
          if (this.hedaoOptions.length > 1) this.hedaoName = this.hedaoOptions[1].value
          // 设置表格数据
          this.waterLevelData = waterLevelData
          this.flowData = flowData

          // 组合成为 GeoJSON 格式
          this.shuiZhaGeojson = {
            type: 'FeatureCollection',
            features: shuiZhaPoints.map(el => {
              return {
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+el.longitude, +el.latitude],
                },
                properties: {
                  ...el,
                },
              }
            }),
          }
          // 渠道数据
          this.hedaoChartData = hedaoChartDataTemp
          // 更新折线图数据 - 使用第二个河道的数据（data[1]）
          if (riverData.length > 1 && riverData[1].sites) {
            chartData = riverData[1].sites.map(site => [
              site.mileage, // 横坐标：渠道断面（里程）
              site.wlv, // 纵坐标：水位
            ])

            this.chartData = {
              dataSource: [
                {
                  name: '水位',
                  color: '#1890ff',
                  data: chartData,
                },
              ],
              custom: {
                shortValue: true,
                dataZoom: false,
                showAreaStyle: false,
                xLabel: 'm',
                yLabel: '水位(m)',
                legend: false,
                grid: {
                  left: '10%',
                  right: '10%',
                  top: '15%',
                  bottom: '15%',
                  containLabel: true,
                },
              },
            }
          }
        }
      },
      immediate: true,
      deep: true,
    },
    hedaoName: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          if (Object.keys(this.hedaoChartData).length > 0 && this.hedaoChartData[newVal]) {
            let { wlv, flow, mileage } = this.hedaoChartData[this.hedaoName]
            console.log(wlv, flow, mileage)
            this.lineChartCustom.xAxisData = mileage
            this.lineChartCustom.yMax0 = Math.max(...wlv)
            this.lineChartCustom.yMin0 = Math.min(...wlv)
            this.lineChartData = [
              {
                name: '实时水位',
                color: '#507EF7',
                yAxisIndex: 0,
                data: wlv,
              }
            ]
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    getInWaterPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
      //调度方案选项
      this.dispatchCaseOptions = (res.data?.data || []).map(el => ({
        ...el,
        label: el.caseName,
        value: el.inWaterId,
      }))
      if (this.dispatchCaseOptions.length > 0) {
        this.dispatchCase = this.dispatchCaseOptions[0].value
        // 初始化时加载来水方案数据
        this.loadMaxOutWaterData(this.dispatchCase)
      }
    })
  },
  beforeDestroy() {
    // 清理防抖定时器
    if (this.saveDebounceTimer) {
      clearTimeout(this.saveDebounceTimer)
      this.saveDebounceTimer = null
    }
  },
  methods: {
    //添加以上两个图层之后 再添加这个函数，参数是动态线的图层配置
    addDashLayer(sourceLayerConfig) {
      const self = this
      const dashArraySequence = [
        [0, 4, 3],
        [0.5, 4, 2.5],
        [1, 4, 2],
        [1.5, 4, 1.5],
        [2, 4, 1],
        [2.5, 4, 0.5],
        [3, 4, 0],
        [0, 0.5, 3, 3.5],
        [0, 1, 3, 3],
        [0, 1.5, 3, 2.5],
        [0, 2, 3, 2],
        [0, 2.5, 3, 1.5],
        [0, 3, 3, 1],
        [0, 3.5, 3, 0.5],
      ]
      let step = 0
      function animateDashArray(timestamp) {
        // Update line-dasharray using the next value in dashArraySequence. The
        // divisor in the expression `timestamp / 50` controls the animation speed.
        const newStep = parseInt((timestamp / 100) % dashArraySequence.length)

        if (newStep !== step) {
          let layer = self.mapIns.getLayer(sourceLayerConfig.id) //获取图层
          // debugger
          if (layer) {
            self.mapIns.setPaintProperty(sourceLayerConfig.id, 'line-dasharray', dashArraySequence[step])
            step = newStep
          }
        }

        // Request the next frame of the animation.
        requestAnimationFrame(animateDashArray)
      }
      // start the animation
      animateDashArray(0)
    },
    async dealLayers() {
      if (!!this.mapIns && !!this.shuiZhaGeojson) {
        // 灌区的边界
        await axios(
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP004&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo></Or>`,
        ).then(res => {
          mapBoundGeo(res.data, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
          this.mapIns.addLayer({
            id: 'geojson-layer-guanqu',
            type: 'line',
            source: {
              type: 'geojson',
              lineMetrics: true,
              data: res.data,
            },
            paint: {
              'line-width': 1,
              'line-opacity': 1,
              'line-color': '#2BBCFF',
            },
          })
        })
        // 沟渠
        let hoveredPolylineId = null
        await axios(
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP005&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>2</Literal></PropertyIsEqualTo></Or>`,
        ).then(res => {
          res.data.features.forEach(el => {
            el.geometry.coordinates.forEach(item => item.reverse())
          })
          this.mapIns.addSource('guanqu-source', {
            type: 'geojson',
            data: res.data,
            generateId: true,
          })
          this.mapIns.addLayer({
            id: 'geojson-layer-gouqu',
            type: 'line',
            source: 'guanqu-source',
            paint: {
              'line-width': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                5,
                ['case', ['==', ['get', 'map_class'], '1'], 3, 2],
              ],
              'line-opacity': 1,
              'line-color': 'rgba(255,255,255,0.5)',
            },
          })
          this.mapIns.addLayer({
            id: 'geojson-layer-gouqu1',
            type: 'line',
            source: 'guanqu-source',
            paint: {
              'line-width': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                5,
                ['case', ['==', ['get', 'map_class'], '1'], 3, 2],
              ],
              'line-opacity': 1,
              'line-color': '#12E952',
            },
          })
          this.addDashLayer({
            id: 'geojson-layer-gouqu1',
          })
          this.mapIns.addInteraction('geojson-layer-gouqu1-click-interaction', {
            type: 'click',
            target: { layerId: 'geojson-layer-gouqu1' },
            handler: e => {
              // Copy coordinates array.
              const coordinates = e.lngLat
              const outerHtml = document.createElement('div')
              outerHtml.className = 'outerPaiShuiLine'
              const description = e.feature.properties.object_name
              this.hedaoName = this.hedaoOptions.find(item => item.label == description).value
              outerHtml.textContent = description
              // this.extractData(e.feature.properties.id)
              new mapboxgl.Popup().setLngLat(coordinates).setDOMContent(outerHtml).addTo(this.mapIns)
            },
          })
          this.mapIns.on('mousemove', 'geojson-layer-gouqu1', e => {
            if (e.features.length > 0) {
              if (hoveredPolylineId !== null) {
                this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: false })
              }
              hoveredPolylineId = e.features[0].id
              this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: true })
            }
          })
          this.mapIns.on('mouseleave', 'geojson-layer-gouqu1', () => {
            if (hoveredPolylineId !== null) {
              this.mapIns.setFeatureState({ source: 'guanqu-source', id: hoveredPolylineId }, { hover: false })
            }
            hoveredPolylineId = null
          })
        })
        // 水库边界
        await axios(
          `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=thjgq:HP001&maxFeatures=50&outputFormat=application/json&filter=<PropertyIsEqualTo><PropertyName>object_name</PropertyName><Literal>桃花江水库</Literal></PropertyIsEqualTo>`,
        ).then(res => {
          this.mapIns.addLayer({
            id: 'geojson-layer-shuiku',
            type: 'fill',
            source: {
              type: 'geojson',
              lineMetrics: true,
              data: res.data,
            },
            paint: {
              'fill-color': '#12E952',
            },
          })
          let coordinates = turf.center(res.data.features[0]).geometry.coordinates
          let onProcessClickFunc = item => {
            // 如果选择的是不同的来水方案，重新加载数据
            if (this.dispatchCase !== item.dispatchCase) {
                this.dispatchCase = item.dispatchCase
                this.loadMaxOutWaterData(item.dispatchCase)
                this.supplyDemandData.actualSupply = item?.sjWater
              } else {
                // 如果是同一个方案，只更新实际可供水量
                if (this.maxOutWaterData) {
                  // 使用Vue.set确保响应式更新，并转换为数字
                  this.$set(this.maxOutWaterData, 'thisOutWater', Number(item.sjWater))
                }
                this.supplyDemandData.actualSupply = item?.sjWater
              }
          }
          let shuikuPopupIns = mapboxShuikuPopup(this.mapIns, {
            dispatchCaseOptions: [...this.dispatchCaseOptions],
            lngLat: coordinates,
            maxOutWaterData: this.maxOutWaterData,
            onPopupClose: item => {
              shuikuPopupIns.remove()
              shuikuPopupIns = null
            },
            onProcessClick: onProcessClickFunc
          })
          this.mapIns.addInteraction('geojson-layer-shuiku-click-interaction', {
            type: 'click',
            target: { layerId: 'geojson-layer-shuiku' },
            handler: e => {
              if (shuikuPopupIns) return
              shuikuPopupIns = mapboxShuikuPopup(this.mapIns, {
                dispatchCaseOptions: [...this.dispatchCaseOptions],
                lngLat: coordinates,
                maxOutWaterData: this.maxOutWaterData,
                onPopupClose: item => {
                  shuikuPopupIns.remove()
                  shuikuPopupIns = null
                },
                onProcessClick: onProcessClickFunc
              })
            },
          })
        })

        // 水闸
        this.mapIns.addLayer({
            id: 'geojson-layer-points',
            type: 'circle',
            source: {
              type: 'geojson',
              lineMetrics: true,
              data: this.shuiZhaGeojson,
            },
            paint: {
              'circle-radius': 4,
              'circle-color': '#12E952',
              'circle-stroke-width': 1,
              'circle-stroke-color': '#fff',
            },
          })
          this.mapIns.addInteraction('geojson-layer-points-click-interaction', {
            type: 'click',
            target: { layerId: 'geojson-layer-points' },
            handler: e => {
              const coordinates = e.lngLat
              let index = showPopupItem.findIndex(el => el.siteCode == e.feature.properties.siteCode)
              if (index != -1) return
              let curr = e.feature.properties
              const popupIns = mapboxShuiZhaPopup(this.mapIns, {
                ...curr,
                lngLat: coordinates,
                onPopupClose: item => {
                  const index = showPopupItem.findIndex(el => el.siteCode == item.siteCode)
                  if (index === -1) return
                  showPopupItem[index].popupIns.remove()
                  showPopupItem = showPopupItem.filter((el, i) => i !== index)
                },
                onProcessClick: item => {
                  this.waterLevelData = this.waterLevelData.map(el => {
                    if (el.dispatchObject == item.siteName) {
                      return { dispatchObject:item.siteName, waterLevel: item.wlv }
                    }
                    return el
                  })
                  console.log('点击了确定按钮',item)
                }
              })
              showPopupItem.push({ ...curr, popupIns })
            },
          })
          this.mapIns.addLayer({
            id: 'geojson-layer-points1',
            type: 'symbol',
            source: {
              type: 'geojson',
              lineMetrics: true,
              data: this.shuiZhaGeojson,
            },
            layout: {
              'text-field': ['get', 'siteName'],
              'text-size': 10,
              'text-justify': 'right',
              'text-offset': [0.5, 0.5],
              'text-anchor': 'left',
            },
            paint: {
              'text-color': '#000',
              'text-halo-blur': 1,
              'text-halo-color': '#fff',
              'text-halo-width': 1.5,
            },
          })
      }
    },
    // 渲染可编辑单元格
    renderEditableCell(row, rowIndex, field, tableType) {
      const h = this.$createElement

      return h(
        'div',
        {
          class: 'editable-cell',
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            width: '100%',
            padding: '4px',
          },
          on: {
            mouseenter: () => this.handleCellMouseEnter(rowIndex, field, tableType),
            mouseleave: () => this.handleCellMouseLeave(),
          },
        },
        [
          h('a-input-number', {
            props: {
              size: 'small',
              step: 0.01,
              min: 0,
              precision: 2,
              value: row[field],
            },
            style: {
              width: '100px',
            },
            on: {
              change: value => this.handleInputChange(rowIndex, field, value, tableType),
            },
          }),
          h(
            'a',
            {
              style: {
                color: '#165DFF',
                textDecoration: 'none',
                fontSize: '12px',
                whiteSpace: 'nowrap',
                opacity:
                  this.hoveredRowIndex === rowIndex &&
                    this.hoveredField === field &&
                    this.hoveredTableType === tableType
                    ? 1
                    : 0,
                transition: 'opacity 0.2s ease',
                cursor: 'pointer',
              },
              on: {
                click: () => this.handleFillDown(rowIndex, field, tableType),
              },
            },
            '向下填充',
          ),
        ],
      )
    },

    // 处理单元格鼠标进入
    handleCellMouseEnter(rowIndex, field, tableType) {
      this.hoveredRowIndex = rowIndex
      this.hoveredField = field
      this.hoveredTableType = tableType
    },

    // 处理单元格鼠标离开
    handleCellMouseLeave() {
      this.hoveredRowIndex = -1
      this.hoveredField = ''
      this.hoveredTableType = ''
    },

    // 处理输入框数值变化
    handleInputChange(rowIndex, field, value, tableType) {
      let targetData
      if (tableType === 'supplyDemand') {
        targetData = this.supplyDemandData.tableData
      } else if (tableType === 'waterLevel') {
        targetData = this.waterLevelData
      } else if (tableType === 'flow') {
        targetData = this.flowData
      }

      if (targetData && targetData[rowIndex]) {
        const newData = [...targetData]
        newData[rowIndex] = {
          ...newData[rowIndex],
          [field]: value || 0,
        }

        if (tableType === 'supplyDemand') {
          this.supplyDemandData.tableData = newData
        } else if (tableType === 'waterLevel') {
          this.waterLevelData = newData
        } else if (tableType === 'flow') {
          this.flowData = newData
        }
      }
    },

    // 处理向下填充
    handleFillDown(fromIndex, field, tableType) {
      let targetData, fieldName
      if (tableType === 'supplyDemand') {
        targetData = this.supplyDemandData.tableData
        fieldName = '需水量'
      } else if (tableType === 'waterLevel') {
        targetData = this.waterLevelData
        fieldName = '水位'
      } else if (tableType === 'flow') {
        targetData = this.flowData
        fieldName = '流量'
      }

      if (!targetData || !targetData[fromIndex]) return

      const fillValue = targetData[fromIndex][field]
      const newData = [...targetData]

      // 向下填充到所有后续行
      for (let i = fromIndex + 1; i < newData.length; i++) {
        newData[i] = {
          ...newData[i],
          [field]: fillValue,
        }
      }

      if (tableType === 'supplyDemand') {
        this.supplyDemandData.tableData = newData
      } else if (tableType === 'waterLevel') {
        this.waterLevelData = newData
      } else if (tableType === 'flow') {
        this.flowData = newData
      }

      // 隐藏悬浮状态
      this.hoveredRowIndex = -1
      this.hoveredField = ''
      this.hoveredTableType = ''

      this.$message.success(`已将${fieldName}向下填充`)
    },

    // 加载供需水数据
    async loadSupplyDemandData() {
      if (!this.baseInfo || !this.baseInfo.startTime || !this.baseInfo.endTime) {
        return
      }

      try {
        const params = {
          startTime: this.baseInfo.startTime,
          endTime: this.baseInfo.endTime,
          scene: this.baseInfo.simulateType,
        }

        const response = await getChDeptFlow(params)
        console.log('供需水数据响应:', response)

        if (response.data) {
          const { records, supplyWaterValue } = response.data

          // 设置实际可供水量
          this.supplyDemandData.actualSupply = supplyWaterValue

          // 保存原始的完整数据
          this.supplyDemandData.originalRecords = records || []

          // 设置需水口数据，初始需水量为0
          this.supplyDemandData.tableData = (records || []).map(item => ({
            waterIntakeName: item.deptName, // 需水口名称
            waterDemandValue: 0, // 需水量(万m³) - 初始化为0
          }))
        }
      } catch (error) {
        console.error('获取供需水数据失败:', error)
        this.$message.error('获取供需水数据失败')
      }
    },

    // 加载水位流量数据
    async loadWaterFlowData() {
      try {
        const response = await getChWaterList()
        console.log('水位流量数据响应:', response)

        if (response.data) {
          const riverData = response.data || []

          // 处理水位和流量数据
          let waterLevelData = []
          let flowData = []
          let chartData = []
          // 用于存储水闸的坐标与属性信息
          let shuiZhaPoints = []
          // 整理渠道横断面数据
          let hedaoChartDataTemp = {}
          // 遍历所有河道数据
          riverData.forEach(river => {
            // 处理每个河道的站点数据转换为echarts可以使用的数据
            let tempObj = (hedaoChartDataTemp[river.projectCode] = {})
            tempObj['code'] = river.projectCode
            tempObj['name'] = river.projectName
            tempObj['wlv'] = []
            tempObj['flow'] = []
            tempObj['mileage'] = []

            if (river.sites && river.sites.length > 0) {
              river.sites.forEach(site => {
                // 水位数据
                waterLevelData.push({
                  dispatchObject: site.siteName,
                  waterLevel: site.wlv,
                })

                // 流量数据
                flowData.push({
                  dispatchObject: site.siteName,
                  flow: site.flow,
                })

                // 渠道横断面数据
                tempObj['wlv'].push(+site.wlv)
                tempObj['mileage'].push(+site.mileage)
                tempObj['flow'].push(+site.flow)

                // 水闸所有属性信息
                shuiZhaPoints.push({
                  ...site,
                  lineName: river.projectName,
                  lineCode: river.projectCode,
                  projectName: site.siteName,
                  projectCode: site.siteCode,
                })
              })
            }
            if (tempObj['mileage'].length && tempObj['mileage'][0] != 0) {
              tempObj['wlv'].unshift(tempObj['wlv'][0])
              tempObj['flow'].unshift(tempObj['flow'][0])
              tempObj['mileage'].unshift(0)
            }
            if (tempObj['mileage'].length && (tempObj['mileage'][tempObj['mileage'].length - 1] != river.endMileage)) {
              tempObj['wlv'].push(tempObj['wlv'][tempObj['wlv'].length - 1])
              tempObj['flow'].push(tempObj['flow'][tempObj['flow'].length - 1])
              tempObj['mileage'].push(river.endMileage)
            }
            tempObj['wlv'] = tempObj['wlv'].map(el => Number(el).toFixed(2))
            tempObj['flow'] = tempObj['flow'].map(el => Number(el).toFixed(2))
          })
          this.hedaoOptions = Object.keys(hedaoChartDataTemp).map(key => ({
            label: hedaoChartDataTemp[key].name,
            value: hedaoChartDataTemp[key].code,
          }))
          if (this.hedaoOptions.length > 1) this.hedaoName = this.hedaoOptions[1].value
          // 设置表格数据
          this.waterLevelData = waterLevelData
          this.flowData = flowData

          // 组合成为 GeoJSON 格式
          this.shuiZhaGeojson = {
            type: 'FeatureCollection',
            features: shuiZhaPoints.map(el => {
              return {
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+el.longitude, +el.latitude],
                },
                properties: {
                  ...el,
                },
              }
            }),
          }
          // 渠道数据
          this.hedaoChartData = hedaoChartDataTemp
          // 更新折线图数据 - 使用第二个河道的数据（data[1]）
          if (riverData.length > 1 && riverData[1].sites) {
            chartData = riverData[1].sites.map(site => [
              site.mileage, // 横坐标：渠道断面（里程）
              site.wlv, // 纵坐标：水位
            ])

            this.chartData = {
              dataSource: [
                {
                  name: '水位',
                  color: '#1890ff',
                  data: chartData,
                },
              ],
              custom: {
                shortValue: true,
                dataZoom: false,
                showAreaStyle: false,
                xLabel: 'm',
                yLabel: '水位(m)',
                legend: false,
                grid: {
                  left: '10%',
                  right: '10%',
                  top: '15%',
                  bottom: '15%',
                  containLabel: true,
                },
              },
            }
          }
        }
      } catch (error) {
        console.error('获取水位流量数据失败:', error)
        this.$message.error('获取水位流量数据失败')
      }
    },

    onMapMounted(mapIns) {
      this.mapIns = mapIns
      this.$nextTick(() => {
        this.mapIns.resize()
      })
      this.dealLayers()
    },
    onTabChange(activeKey) {
      this.tableData = this.allData.find(el => el.projectId === activeKey).res
      this.tableColumns = this.allData.find(el => el.projectId === activeKey).columns
      this.tableKey += 1
    },
    // 供需水批量导入
    handleSupplyDemandBatchImport() {
      this.batchImportModal = {
        visible: true,
        nameList: this.supplyDemandData.tableData.map(item => item.waterIntakeName),
        sourceType: 'waterDemandValue',
      }
    },

    // 水位批量导入
    handleWaterLevelBatchImport() {
      this.batchImportModal = {
        visible: true,
        nameList: this.waterLevelData.map(item => item.dispatchObject),
        sourceType: 'waterLevel',
      }
    },

    // 流量批量导入
    handleFlowBatchImport() {
      this.batchImportModal = {
        visible: true,
        nameList: this.flowData.map(item => item.dispatchObject),
        sourceType: 'flow',
      }
    },

    // 批量导入保存
    handleBatchImportSave(importData) {
      if (this.batchImportModal.sourceType === 'waterDemandValue') {
        // 处理供需水数据
        const newData = this.supplyDemandData.tableData.map((item, idx) => {
          const importItem = importData[idx]
          if (!importItem) return { ...item }
          return {
            ...item,
            waterDemandValue: importItem.waterDemandValue || 0,
          }
        })
        this.supplyDemandData.tableData = newData
      } else if (this.batchImportModal.sourceType === 'waterLevel') {
        // 处理水位数据
        const newData = this.waterLevelData.map((item, idx) => {
          const importItem = importData[idx]
          if (!importItem) return { ...item }
          return {
            ...item,
            waterLevel: importItem.waterLevel || 0,
          }
        })
        this.waterLevelData = newData
      } else if (this.batchImportModal.sourceType === 'flow') {
        // 处理流量数据
        const newData = this.flowData.map((item, idx) => {
          const importItem = importData[idx]
          if (!importItem) return { ...item }
          return {
            ...item,
            flow: importItem.flow || 0,
          }
        })
        this.flowData = newData
      }

      this.batchImportModal.visible = false
      this.$message.success('批量导入成功')
    },

    // 加载来水方案数据
    async loadMaxOutWaterData(inWaterId) {
      if (!inWaterId) return
      try {
        this.isLoadingWaterData = true
        const response = await getMaxOutWater({ inWaterId })
        console.log('来水方案数据响应:', response)
        if (response && response.success && response.data) {
          this.maxOutWaterData = response.data
        }
      } catch (error) {
        console.error('获取来水方案数据失败:', error)
        this.$message.error('获取来水方案数据失败')
      } finally {
        this.isLoadingWaterData = false
      }
    },

    save() {
      // 如果正在保存中，直接返回
      if (this.isSaving) {
        console.log('正在保存中，请勿重复操作')
        return
      }
      
      // 防抖处理：清除之前的定时器
      if (this.saveDebounceTimer) {
        clearTimeout(this.saveDebounceTimer)
      }
      
      // 设置防抖定时器，500ms 内只能执行一次
      this.saveDebounceTimer = setTimeout(() => {
        this.handleSave()
        this.saveDebounceTimer = null
      }, 500)
    },

    async handleSave() {
      // 设置保存状态
      this.isSaving = true
      
      try {
        // 检查来水方案数据是否已加载
        if (!this.maxOutWaterData) {
          this.$message.warning('请等待来水方案模型加载完成')
          this.$emit('saveData', false)
          return
        }
        console.log('测试来水方案最大可供水量', this.maxOutWaterData.maxOutWater)
        //检查来水方案是否有最大可供水量
        if (!this.maxOutWaterData.maxOutWater) {
          this.$message.warning('请重新选择来水方案，该方案无最大可供水量')
          this.$emit('saveData', false)
          return
        }

        //检查来水方案是否有最大可供水量
        if (!this.maxOutWaterData.thisOutWater) {
          this.$message.warning('请重新输入实际可供水量')
          this.$emit('saveData', false)
          return
        }
        // 调用保存数据方法
        await this.saveFormData()
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败，请重试')
        this.$emit('saveData', false)
      } finally {
        // 重置保存状态
        this.isSaving = false
      }
    },
    // 保存数据（调用forecast API获取chSimId）
    async saveFormData() {
      try {
        // 调度方案 默认第一个
        if (this.dispatchCase) {
          // 获取当前的供需水和水位流量数据
          const params = {
            startTime: this.baseInfo.startTime,
            endTime: this.baseInfo.endTime,
            scene: this.baseInfo.simulateType,
          }
          console.log('复制数据 inWaterEchoData', this.inWaterEchoData)
          const chDeptFlowRes = await getChDeptFlow(params)
          const waterFlowRes = await getChWaterList()

          //易炜豪加的暂时测试通过代码，等蒋浩改好必须要删除！！！！！！！！！！！
          // this.maxOutWaterData.thisOutWater=720

          // 构建预报参数
          const forecastParams = {
            ...this.baseInfo,
            scene: this.baseInfo.simulateType,
            inWaterId: this.dispatchCase,
            chDeptFlows: {
              records: this.supplyDemandData.originalRecords.map((originalItem, index) => ({
                ...originalItem, // 保留原始的 deptCode, latitude, longitude 等字段
                waterDemandValue: Number(this.supplyDemandData.tableData[index]?.waterDemandValue) || 0 // 使用用户修改的需水量
              })),
              sumSupplyValue: this.totalDemandWater, // 总需水量
              supplyWaterValue: Number(this.supplyDemandData.actualSupply) || 0 // 可供水量
            },
            chSiteWaters: this.inWaterEchoData ? this.inWaterEchoData.chSiteWaters : waterFlowRes.data,
            outWaters: this.maxOutWaterData, // 使用已加载的来水方案数据
          }

          console.log('调用forecast API，参数:', forecastParams)
          console.log('当前maxOutWaterData:', this.maxOutWaterData)
          console.log('outWaters中的thisOutWater:', this.maxOutWaterData?.thisOutWater)
          console.log('chDeptFlows.records:', forecastParams.chDeptFlows.records)
          console.log('supplyDemandData.tableData:', this.supplyDemandData.tableData)
          console.log('totalDemandWater:', this.totalDemandWater)

          // 调用forecast API执行预报，获取chSimId
          const forecastRes = await forecast(forecastParams)

          console.log('forecast API响应:', forecastRes)

          if (forecastRes && forecastRes.success && forecastRes.data !== undefined) {
            // forecast API返回chSimId
            const chSimId = forecastRes.data
            console.log('获取到chSimId:', chSimId)
            this.$message.success('仿真方案生成成功')
            this.$emit('saveData', chSimId)
          } else {
            console.error('forecast API返回数据异常:', forecastRes)
            this.$message.error(forecastRes.message || '仿真方案生成失败')
            this.$emit('saveData', false)
          }
        }
      } catch (error) {
        console.error('执行预报失败:', error)
        this.$message.error('执行预报失败，请重试')
        this.$emit('saveData', false)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.case-compile-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;

  .page-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 20px;
    height: calc(100% - 50px);
  }

  .left-section {
    flex: 5;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .right-section {
    flex: 4;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    // margin-bottom: 12px;
    color: #333;
  }

  .supply-demand-section {
    flex: 1;
    // padding: 16px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    .supply-input {
      display: flex;
      align-items: center;

      label {
        font-weight: 500;
        color: #333;
      }
    }

    .water-inputs {
      display: flex;
      gap: 24px;
      margin-bottom: 16px;
      align-items: center;
    }

    .supply-input,
    .total-demand-input {
      display: flex;
      align-items: center;

      label {
        font-weight: 500;
        color: #333;
        white-space: nowrap;
      }
    }

    .table-container {
      height: calc(100% - 100px);
    }
  }

  .bottom-tables {
    flex: 1;
    display: flex;
    gap: 8px;
  }

  .water-level-section,
  .flow-section {
    flex: 1;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .table-container {
      height: calc(100% - 60px);
    }
  }
}

::v-deep .vxe-table--render-default.size--small .vxe-header--row .vxe-header--column .vxe-cell {
  height: 40px !important;
}

::v-deep .outerPaiShuiLine {
  width: 150px;
  // height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  background-color: rgb(83, 132, 254);
}

::v-deep .ant-radio-group {
  .ant-radio-wrapper {
    font-size: inherit;

    span {
      &:last-child {
        padding: 0 0 0 5px;
      }
    }
  }
}

::v-deep .ant-empty-image svg {
  width: 100%;
}

// 可编辑单元格样式
.editable-cell {
  transition: all 0.2s ease;
  border-radius: 4px;

  &:hover {
    background-color: rgba(22, 93, 255, 0.05);
  }

  a {
    transition: all 0.2s ease;

    &:hover {
      color: #3273ff !important;
      text-decoration: underline !important;
    }
  }
}

// 批量导入按钮样式
.batch-import-btn {
  border-color: #165dff;
  color: #fff !important;
  font-size: 12px;
  height: 28px;
  padding: 0 12px;

  &:hover {
    border-color: #3273ff;
    background-color: #3273ff;
  }
}
</style>
