<template>
  <div class="common-table-page">
    <div class="page-header">
      <div class="page-title">桃花江水库设计暴雨</div>
    </div>

    <VxeTable
      ref="vxeTableRef"
      tableTitle=" "
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isAdaptPageSize="true"
      @adaptPageSizeChange="adaptPageSizeChange"
      @refresh="getList"
      :tablePage="false"
      :column-config="{ resizable: true, useKey: true }"
      :merge-cells="mergeCells"
    >
      <div class="table-operations" slot="button">
        <a-button type="primary" @click="handleAdd">
          <a-icon type="plus" />
          新增
        </a-button>
        <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
      </div>
    </VxeTable>

    <!-- 新增/编辑弹窗 -->
    <FormModal v-if="showFormModal" ref="formModalRef" @ok="onOperationComplete" @close="showFormModal = false" />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import FormModal from './modules/FormModal.vue'
  import { getCstDesignStormPage, delCstDesignStorm } from './services'
  import moment from 'moment'
  import excelExport from '@/utils/excelExport.js'
  export default {
    name: 'DesignStorm',
    components: {
      VxeTable,
      FormModal,
    },
    data() {
      return {
        exportLoading: false,
        showFormModal: false,
        queryParams: {
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
        },
        list: [],
        loading: false,
        mergeCells: [], // 合并单元格配置

        // 测试数据
        testData: [
          {
            groupId: 1,
            stage: '初设阶段',
            frequencies: [
              {
                id: 1,
                frequency: 'P=3.33%',
                h1Point: 45.2,
                h3Point: 68.5,
                h6Point: 89.3,
                h12Point: 112.7,
                h24Point: 135.8,
                h1Area: 42.1,
                h3Area: 65.4,
                h6Area: 86.2,
                h12Area: 108.9,
                h24Area: 131.5,
              },
              {
                id: 2,
                frequency: 'P=2%',
                h1Point: 52.8,
                h3Point: 78.9,
                h6Point: 102.4,
                h12Point: 128.6,
                h24Point: 155.2,
                h1Area: 49.7,
                h3Area: 75.8,
                h6Area: 98.6,
                h12Area: 124.3,
                h24Area: 149.8,
              },
              {
                id: 3,
                frequency: 'P=0.2%',
                h1Point: 68.5,
                h3Point: 98.7,
                h6Point: 125.8,
                h12Point: 156.4,
                h24Point: 189.3,
                h1Area: 65.2,
                h3Area: 94.6,
                h6Area: 121.5,
                h12Area: 151.2,
                h24Area: 183.7,
              },
            ],
            fileName: '初设阶段_设计暴雨时程分配.xlsx',
          },
          {
            groupId: 2,
            stage: '施工图阶段',
            frequencies: [
              {
                id: 4,
                frequency: 'P=3.33%',
                h1Point: 46.8,
                h3Point: 70.2,
                h6Point: 91.7,
                h12Point: 115.3,
                h24Point: 139.2,
                h1Area: 43.5,
                h3Area: 67.1,
                h6Area: 88.4,
                h12Area: 111.6,
                h24Area: 134.8,
              },
              {
                id: 5,
                frequency: 'P=2%',
                h1Point: 54.3,
                h3Point: 81.2,
                h6Point: 105.1,
                h12Point: 131.8,
                h24Point: 159.6,
                h1Area: 51.2,
                h3Area: 78.4,
                h6Area: 101.7,
                h12Area: 127.9,
                h24Area: 154.2,
              },
              {
                id: 6,
                frequency: 'P=0.2%',
                h1Point: 70.1,
                h3Point: 101.4,
                h6Point: 129.2,
                h12Point: 160.7,
                h24Point: 194.8,
                h1Area: 67.8,
                h3Area: 97.9,
                h6Area: 125.3,
                h12Area: 156.2,
                h24Area: 189.1,
              },
            ],
            fileName: '施工图阶段_设计暴雨时程分配.xlsx',
          },
        ],

        columns: [
          {
            title: '序号',
            field: 'groupIndex',
            width: 80,
            align: 'center',
          },
          {
            title: '阶段',
            field: 'period',
            width: 120,
            align: 'center',
          },
          {
            title: '频率',
            field: 'frequency',
            minWidth: 100,
            align: 'center',
          },
          {
            title: 'H1点',
            field: 'h1Point',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H3点',
            field: 'h3Point',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H6点',
            field: 'h6Point',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H12点',
            field: 'h12Point',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H24点',
            field: 'h24Point',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H1面',
            field: 'h1Area',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H3面',
            field: 'h3Area',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H6面',
            field: 'h6Area',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H12面',
            field: 'h12Area',
            minWidth: 80,
            align: 'center',
          },
          {
            title: 'H24面',
            field: 'h24Area',
            minWidth: 80,
            align: 'center',
          },
          {
            title: '设计暴雨时程分配',
            field: 'temporalDistributionName',
            width: 200,

            align: 'center',
          },
          {
            title: '操作',
            field: 'operate',
            width: 120,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div>
                    <a onClick={() => this.handleEdit(row)}>编辑</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </div>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    methods: {
      // 获取列表数据
      getList() {
        this.loading = true
        // this.queryParams.pageNum = 1
        getCstDesignStormPage(this.queryParams).then(res => {
          this.list = this.convertTestDataToTableData(res?.data?.data || [])
          this.loading = false

          // this.list = res.data
        })
        // 模拟API调用
        // setTimeout(() => {
        //   // 将测试数据转换为表格数据格式
        //   this.list = this.convertTestDataToTableData()
        //   this.loading = false
        // }, 500)
      },

      // 将测试数据转换为表格数据格式
      convertTestDataToTableData(data) {
        const tableData = []
        data?.forEach((group, groupIndex) => {
          // 处理每个频率组
          group.frequencyList.forEach(freq => {
            // 创建基础对象
            const baseObj = {
              id: freq.id,
              frequency: `P=${freq.frequency}%`,
              groupId: group.id,
              period: group.period,
              temporalDistributionName: group.temporalDistributionName,
              groupIndex: groupIndex + 1, // 添加组序号，从1开始
            }

            // 初始化所有点和面的字段为null
            const timePoints = [1, 3, 6, 12, 24]
            timePoints.forEach(hour => {
              baseObj[`h${hour}Point`] = null
              baseObj[`h${hour}Area`] = null
            })

            // 如果values存在，处理每个值
            if (freq.values && Array.isArray(freq.values)) {
              freq.values.forEach(item => {
                const { hyetalType, hyetalHour, hyetalValue } = item
                // 确定类型前缀（Point或Area）
                const typePrefix = hyetalType === 1 ? 'Point' : 'Area'
                // 构造字段名
                const fieldName = `h${hyetalHour}${typePrefix}`
                // 设置值
                baseObj[fieldName] = hyetalValue
              })
            }

            tableData.push(baseObj)
          })
        })

        // 计算合并单元格
        this.calculateMergeCells(tableData)

        return tableData
      },

      // 计算合并单元格配置
      calculateMergeCells(tableData) {
        const mergeCells = []
        let currentRow = 0

        // 按组ID分组
        const groups = {}
        tableData.forEach((item, index) => {
          if (!groups[item.groupId]) {
            groups[item.groupId] = []
          }
          groups[item.groupId].push(index)
        })

        // 为每个组计算合并单元格
        Object.values(groups).forEach(groupIndexes => {
          const firstRowIndex = groupIndexes[0]
          const groupSize = groupIndexes.length

          if (groupSize > 1) {
            // 序号列 (列索引 0)
            mergeCells.push({
              row: firstRowIndex,
              col: 0,
              rowspan: groupSize,
              colspan: 1,
            })

            // 阶段列 (列索引 1)
            mergeCells.push({
              row: firstRowIndex,
              col: 1,
              rowspan: groupSize,
              colspan: 1,
            })

            // 设计暴雨时程分配列 (列索引 12)
            mergeCells.push({
              row: firstRowIndex,
              col: 12,
              rowspan: groupSize,
              colspan: 1,
            })

            // 操作列 (列索引 13)
            mergeCells.push({
              row: firstRowIndex,
              col: 13,
              rowspan: groupSize,
              colspan: 1,
            })
          }
        })

        this.mergeCells = mergeCells
      },

      // 页面大小改变
      adaptPageSizeChange() {
        // 由于不使用分页，这里可以为空
      },

      // 新增
      handleAdd() {
        this.showFormModal = true
        this.$nextTick(() => {
          this.$refs.formModalRef.add()
        })
      },

      // 编辑
      handleEdit(record) {
        this.showFormModal = true
        this.$nextTick(() => {
          this.$refs.formModalRef.edit(record)
        })
      },

      // 删除
      handleDelete(record) {
        const that = this
        this.$confirm({
          title: '确认删除',
          content: `确认删除阶段"${record.period}"的设计暴雨数据吗？`,
          onOk() {
            return delCstDesignStorm({ id: record.groupId })
              .then(res => {
                that.$message.success(`成功删除1条数据`, 3)
                that.onOperationComplete()
                that.getList()
              })
              .catch(err => {
                that.$message.error('删除失败，请重试')
                console.error('删除操作失败:', err)
              })
          },
        })
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        const columnsList = [
          // {
          //   title: '序号',
          //   field: 'groupIndex',
          //   width: 80,
          //   align: 'center',
          // },
          {
            title: '阶段',
            field: 'period',
            width: 120,
            align: 'center',
          },
          {
            title: '频率',
            field: 'frequency',
            minWidth: 100,
            align: 'center',
          },
          {
            title: 'H1点',
            field: 'h1Point',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H3点',
            field: 'h3Point',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H6点',
            field: 'h6Point',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H12点',
            field: 'h12Point',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H24点',
            field: 'h24Point',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H1面',
            field: 'h1Area',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H3面',
            field: 'h3Area',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H6面',
            field: 'h6Area',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H12面',
            field: 'h12Area',
            minWidth: 60,
            align: 'center',
          },
          {
            title: 'H24面',
            field: 'h24Area',
            minWidth: 60,
            align: 'center',
          },
          {
            title: '设计暴雨时程分配',
            field: 'temporalDistributionName',
            width: 400,

            align: 'left',
          },
        ]
        // 模拟导出
        setTimeout(() => {
          excelExport(columnsList, this.list, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
          this.exportLoading = false
          this.$message.success('导出成功')
        }, 1000)
      },

      // 操作完成后刷新列表
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>

<style lang="less" scoped>
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 12px 20px;

    .page-title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  // 确保表格占满宽度
  :deep(.vxe-table) {
    width: 100% !important;

    .vxe-table--header-wrapper,
    .vxe-table--body-wrapper {
      width: 100% !important;
    }

    .vxe-header--column,
    .vxe-body--column {
      min-width: auto !important;
    }
  }
</style>
