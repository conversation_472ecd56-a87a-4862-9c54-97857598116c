import request from '@/utils/request'

// 分页查询
export function getChSimPage(data) {
  return request({
    url: '/model/ch-sim/page',
    method: 'post',
    data,
  })
}

// 删除
export function deleteChSim(params) {
  return request({
    url: '/model/ch-sim/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 根据水利对象分类编码查询工程
export function getProjectByCategoryId(params) {
  return request({
    url: '/base/project/getProjectByCategoryId',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 配置-列表
export function getChSimRange(params) {
  return request({
    url: '/model/ch-sim/range/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 配置-设置
export function setChSimRange(data) {
  return request({
    url: '/model/ch-sim/range/set',
    method: 'post',
    data,
  })
}

// 获取工程流量过程
export function getChSimFlow(data) {
  return request({
    url: '/model/ch-sim/flow/list',
    method: 'post',
    data,
  })
}

// 执行预报
export function forecast(data) {
  return request({
    url: '/model/ch-sim/forecast',
    method: 'post',
    data,
  })
}

// 推演结果
export function getInferRes(params) {
  return request({
    url: '/model/ch-sim/getInferRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 推演仿真
export function getScaleRes(params) {
  return request({
    url: '/model/ch-sim/getScaleRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 推演概化图-部分水利设施仿真结果列表
export function getScaleResDetails(params) {
  return request({
    url: '/model/ch-sim/getScaleResDetails',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程曲线图
export function getChSimResList(params) {
  return request({
    url: '/model/ch-sim/getChSimResList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 复制
export function getInParameter(params) {
  return request({
    url: '/model/ch-sim/getInParameter',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 保存
export function saveChSim(params) {
  return request({
    url: '/model/ch-sim/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//执行预报
export function saveForecast(params) {
  return request({
    url: '/model/ch-sim/forecast',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//仿真方案编制 数据来源接口
//仿真方案编制-供水量
export function getMaxOutWater(params) {
  return request({
    url: '/model/ch-sim/getMaxOutWater',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取模型详情
export function getChSimDetail(params) {
  return request({
    url: '/model/ch-sim/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//仿真方案编制-水位流量
export function getChWaterList() {
  return request({
    url: '/model/ch-sim/getChWaterList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//仿真方案编制-供需水
export function getChDeptFlow(data) {
  return request({
    url: '/model/ch-sim/getChDeptFlow',
    method: 'post',
    data,
  })
}
// 获取来水方案列表数据
export function getInWaterPage(data) {
  return request({
    url: '/model/in-water/page',
    method: 'post',
    data,
  })
}

// 获取模型运行进度
export function getModelRunProcess(params) {
  return request({
    url: '/model/ch-sim/getModelRunProcess',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取闸门列表
export function getChGateList(params) {
  return request({
    url: '/model/ch-sim/getChGateList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取调度流量结果
export function getModelFlowRes(params) {
  return request({
    url: '/model/ch-sim/getModelFlowRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取供需平衡结果
export function getModelSupplyRes(params) {
  return request({
    url: '/model/ch-sim/getModelSupplyRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
