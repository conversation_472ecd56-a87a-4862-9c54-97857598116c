<template>
  <!-- 新增/编辑弹窗 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    modalHeight="720"
    @cancel="cancel"
  >
    <div slot="content">
      <div class="table-panel">
        <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 2 }" :wrapper-col="{ span: 10 }">
          <!-- 阶段输入框 -->
          <a-form-model-item label="阶段" prop="period">
            <a-input v-model="form.period" placeholder="请输入阶段名称" />
          </a-form-model-item>

          <!-- 设计暴雨参数标题 -->
          <div class="section-title">设计暴雨参数</div>
          <div class="section-unit">单位：mm</div>

          <!-- 参数表格 -->
          <div class="parameter-table">
            <a-table :columns="parameterColumns" :data-source="parameterData" :pagination="false" bordered size="small">
              <template slot="p333" slot-scope="text, record">
                <a-input-number v-model="record.p333" :precision="1" :min="0" style="width: 100%" />
              </template>
              <template slot="p2" slot-scope="text, record">
                <a-input-number v-model="record.p2" :precision="1" :min="0" style="width: 100%" />
              </template>
              <template slot="p02" slot-scope="text, record">
                <a-input-number v-model="record.p02" :precision="1" :min="0" style="width: 100%" />
              </template>
            </a-table>
          </div>

          <!-- 设计暴雨时程分配 -->
          <div class="section-title" style="margin-top: 20px">设计暴雨时程分配</div>
          <!-- -->
          <a-form-model-item prop="ossPath">
            <UploadFile
              :fileUrl.sync="form.ossPath"
              @update:fileUrl="onFileChange"
              :multiple="false"
              listType="text"
              folderName="design-storm"
              accept=".xlsx,.xls"
            />
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'
  import { addCstDesignStorm, updateCstDesignStorm, getWaterSupplyDetails } from '../services'

  export default {
    name: 'FormModal',
    components: { AntModal, UploadFile },
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        open: false,
        isEdit: false,
        ossPath: '',
        // 表单参数
        form: {
          id: undefined,
          period: '',
          // fileUrl: '',
          ossPath: '', //fileurl
          temporalDistributionName: '', //filename
          details: [],
        },

        // 验证规则
        rules: {
          period: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
        },

        // 参数表格列配置
        parameterColumns: [
          {
            title: '项目',
            dataIndex: 'item',
            width: 120,
            align: 'center',
          },
          {
            title: 'P=3.33%',
            dataIndex: 'p333',
            width: 120,
            align: 'center',
            scopedSlots: { customRender: 'p333' },
          },
          {
            title: 'P=2%',
            dataIndex: 'p2',
            width: 120,
            align: 'center',
            scopedSlots: { customRender: 'p2' },
          },
          {
            title: 'P=0.2%',
            dataIndex: 'p02',
            width: 120,
            align: 'center',
            scopedSlots: { customRender: 'p02' },
          },
        ],

        // 参数表格数据
        parameterData: [
          { key: 'h1Point', item: 'H1点', p333: 0, p2: 0, p02: 0 },
          { key: 'h3Point', item: 'H3点', p333: 0, p2: 0, p02: 0 },
          { key: 'h6Point', item: 'H6点', p333: 0, p2: 0, p02: 0 },
          { key: 'h12Point', item: 'H12点', p333: 0, p2: 0, p02: 0 },
          { key: 'h24Point', item: 'H24点', p333: 0, p2: 0, p02: 0 },
          { key: 'h1Area', item: 'H1面', p333: 0, p2: 0, p02: 0 },
          { key: 'h3Area', item: 'H3面', p333: 0, p2: 0, p02: 0 },
          { key: 'h6Area', item: 'H6面', p333: 0, p2: 0, p02: 0 },
          { key: 'h12Area', item: 'H12面', p333: 0, p2: 0, p02: 0 },
          { key: 'h24Area', item: 'H24面', p333: 0, p2: 0, p02: 0 },
        ],
      }
    },
    watch: {},
    methods: {
      onFileChange(filUrl, size) {
        this.ossPath = filUrl
        this.form.ossPath = filUrl
        this.form.temporalDistributionName = filUrl?.split('/')?.pop()
        console.log('* change url', this.form.ossPath, this.ossPath)
        console.log('* change name', this.form.temporalDistributionName)
      },
      // 新增
      add() {
        this.reset()
        this.formTitle = '新增设计暴雨'
        this.isEdit = false
        this.open = true
        this.convertDetailsToParameterData()
      },

      // 编辑
      edit(record) {
        this.reset()
        this.formTitle = '编辑设计暴雨'
        this.isEdit = true
        this.open = true
        // 填充表单数据
        this.form = {
          ...record,
          id: record.groupId,
        }

        getWaterSupplyDetails({ id: record.groupId }).then(res => {
          // 将frequencyList转换为form.details格式
          const transformedDetails = []
          res?.data?.frequencyList?.forEach(freqItem => {
            const frequency = freqItem.frequency
            freqItem.values?.forEach(valueItem => {
              transformedDetails.push({
                frequency: frequency,
                hyetalHour: valueItem.hyetalHour,
                hyetalType: valueItem.hyetalType,
                hyetalValue: valueItem.hyetalValue,
              })
            })
          })
          this.form.details = transformedDetails || []
          this.form.ossPath = res?.data?.ossPath
          // this.form.ossPath = res?.data?.ossPath ? [res?.data?.ossPath] : null
          console.log('*  详情 url', this.form, this.form.ossPath)
          // 数据加载完成后再转换
          this.convertDetailsToParameterData()
        })
      },

      // 重置表单
      reset() {
        this.form = {
          id: undefined,
          period: '',
          // fileUrl: '',
          ossPath: '', //fileurl
          temporalDistributionName: '', //filename
          details: [],
        }

        // 重置参数表格
        this.parameterData.forEach(item => {
          item.p333 = 0
          item.p2 = 0
          item.p02 = 0
        })

        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
      },

      // 提交表单
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            // 将parameterData转换为details
            this.form.details = this.convertParameterDataToDetails()
            this.form.ossPath = this.ossPath
            this.form.temporalDistributionName = !this.form.temporalDistributionName
              ? this.form.ossPath?.split('/').pop()
              : this.form.temporalDistributionName
            console.log('*  提交  ', this.form)
            // 提交数据
            const request = this.form.id ? updateCstDesignStorm : addCstDesignStorm
            request(this.form)
              .then(res => {
                if (res.code === 200) {
                  this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
                  this.loading = false
                  this.cancel()
                  this.$emit('ok')
                } else {
                  this.$message.error(res.msg || (this.isEdit ? '编辑失败' : '新增失败'))
                  this.loading = false
                }
              })
              .catch(err => {
                this.$message.error(this.isEdit ? '编辑失败' : '新增失败')
                this.loading = false
              })
          }
        })
      },

      // 取消
      cancel() {
        this.open = false
        this.reset()
        this.$emit('close')
      },

      // 将details转换为parameterData
      convertDetailsToParameterData() {
        // 重置所有参数值
        this.parameterData.forEach(item => {
          item.p333 = 0
          item.p2 = 0
          item.p02 = 0
        })

        // 遍历details填充数据
        this.form.details.forEach(item => {
          // 确定时间点和类型
          const hyetalHour = item.hyetalHour
          const hyetalType = item.hyetalType
          const frequency = item.frequency
          const hyetalValue = item.hyetalValue

          // 确定key前缀
          let keyPrefix = `h${hyetalHour}`
          // 确定key后缀
          let keySuffix = hyetalType === 1 ? 'Point' : 'Area'
          // 构建完整key
          let key = `${keyPrefix}${keySuffix}`

          // 找到对应的parameterData项
          const paramItem = this.parameterData.find(param => param.key === key)
          if (paramItem) {
            // 根据频率设置对应的值
            if (frequency === 3.33) {
              paramItem.p333 = hyetalValue
            } else if (frequency === 2) {
              paramItem.p2 = hyetalValue
            } else if (frequency === 0.2) {
              paramItem.p02 = hyetalValue
            }
          }
        })
      },

      // 将parameterData转换为details
      convertParameterDataToDetails() {
        const details = []

        this.parameterData.forEach(item => {
          // 解析key获取时间点和类型
          const key = item.key
          // 匹配h数字部分
          const hourMatch = key.match(/h(\d+)/)
          // 匹配Point或Area部分
          const typeMatch = key.match(/(Point|Area)/)

          if (hourMatch && typeMatch) {
            const hyetalHour = parseInt(hourMatch[1])
            const hyetalType = typeMatch[1] === 'Point' ? 1 : 2

            // 处理3.33%频率
            if (item.p333 !== null && item.p333 !== undefined) {
              details.push({
                frequency: 3.33,
                hyetalHour: hyetalHour,
                hyetalType: hyetalType,
                hyetalValue: item.p333,
              })
            }

            // 处理2%频率
            if (item.p2 !== null && item.p2 !== undefined) {
              details.push({
                frequency: 2,
                hyetalHour: hyetalHour,
                hyetalType: hyetalType,
                hyetalValue: item.p2,
              })
            }

            // 处理0.2%频率
            if (item.p02 !== null && item.p02 !== undefined) {
              details.push({
                frequency: 0.2,
                hyetalHour: hyetalHour,
                hyetalType: hyetalType,
                hyetalValue: item.p02,
              })
            }
          }
        })

        return details
      },
    },
  }
</script>

<style lang="less" scoped>
  .table-panel {
    padding: 10px 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 20px 0 10px 0;
    padding-left: 10px;
    border-left: 4px solid #1890ff;
  }
  .section-unit {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    padding-left: 10px;
  }

  .parameter-table {
    margin: 10px 0;

    /deep/ .ant-table-tbody > tr > td {
      padding: 8px;
    }

    /deep/ .ant-input-number {
      border-radius: 4px;
    }
  }
</style>
